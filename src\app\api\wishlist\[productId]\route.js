import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import connectDB from '@/lib/mongoose';
import Wishlist from '@/models/Wishlist';

export async function DELETE(request, { params }) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    await connectDB();
    
    const resolvedParams = await params;
    const productId = resolvedParams.productId;
    
    const wishlist = await Wishlist.findOne({ user: session.user.id });
    
    if (!wishlist) {
      return NextResponse.json(
        { error: 'Wishlist not found' },
        { status: 404 }
      );
    }

    // Check if product is in wishlist
    const itemExists = wishlist.items.some(
      item => item.product.toString() === productId
    );

    if (!itemExists) {
      return NextResponse.json(
        { error: 'Product not in wishlist' },
        { status: 404 }
      );
    }

    // Remove product from wishlist
    await wishlist.removeItem(productId);
    
    // Populate product data for response
    await wishlist.populate({
      path: 'items.product',
      select: 'title price salePrice images category status',
    });
    
    return NextResponse.json(wishlist);
  } catch (error) {
    console.error('Error removing from wishlist:', error);
    return NextResponse.json(
      { error: 'Failed to remove from wishlist' },
      { status: 500 }
    );
  }
}

export async function GET(request, { params }) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    await connectDB();
    
    const resolvedParams = await params;
    const productId = resolvedParams.productId;
    
    const wishlist = await Wishlist.findOne({ user: session.user.id });
    
    if (!wishlist) {
      return NextResponse.json({ inWishlist: false });
    }

    const inWishlist = wishlist.hasProduct(productId);
    
    return NextResponse.json({ inWishlist });
  } catch (error) {
    console.error('Error checking wishlist status:', error);
    return NextResponse.json(
      { error: 'Failed to check wishlist status' },
      { status: 500 }
    );
  }
}
