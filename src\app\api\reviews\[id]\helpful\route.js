import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import connectDB from '@/lib/mongoose';
import Review from '@/models/Review';

export async function POST(request, { params }) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    await connectDB();
    
    const resolvedParams = await params;
    const { helpful } = await request.json(); // true for helpful, false for not helpful
    
    if (typeof helpful !== 'boolean') {
      return NextResponse.json(
        { error: 'Invalid helpful value' },
        { status: 400 }
      );
    }

    const review = await Review.findById(resolvedParams.id);
    
    if (!review) {
      return NextResponse.json(
        { error: 'Review not found' },
        { status: 404 }
      );
    }

    // Check if user already voted
    const existingVoteIndex = review.helpful.findIndex(
      vote => vote.user.toString() === session.user.id
    );

    if (existingVoteIndex !== -1) {
      // Update existing vote
      review.helpful[existingVoteIndex].helpful = helpful;
    } else {
      // Add new vote
      review.helpful.push({
        user: session.user.id,
        helpful,
      });
    }

    await review.save();
    
    // Calculate new helpful score
    const helpfulCount = review.helpful.filter(h => h.helpful).length;
    const notHelpfulCount = review.helpful.filter(h => !h.helpful).length;
    const helpfulScore = helpfulCount - notHelpfulCount;
    
    return NextResponse.json({
      helpfulScore,
      helpfulCount,
      notHelpfulCount,
      userVote: helpful,
    });
  } catch (error) {
    console.error('Error updating helpful vote:', error);
    return NextResponse.json(
      { error: 'Failed to update helpful vote' },
      { status: 500 }
    );
  }
}

export async function DELETE(request, { params }) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    await connectDB();
    
    const resolvedParams = await params;
    const review = await Review.findById(resolvedParams.id);
    
    if (!review) {
      return NextResponse.json(
        { error: 'Review not found' },
        { status: 404 }
      );
    }

    // Remove user's vote
    review.helpful = review.helpful.filter(
      vote => vote.user.toString() !== session.user.id
    );

    await review.save();
    
    // Calculate new helpful score
    const helpfulCount = review.helpful.filter(h => h.helpful).length;
    const notHelpfulCount = review.helpful.filter(h => !h.helpful).length;
    const helpfulScore = helpfulCount - notHelpfulCount;
    
    return NextResponse.json({
      helpfulScore,
      helpfulCount,
      notHelpfulCount,
      userVote: null,
    });
  } catch (error) {
    console.error('Error removing helpful vote:', error);
    return NextResponse.json(
      { error: 'Failed to remove helpful vote' },
      { status: 500 }
    );
  }
}
