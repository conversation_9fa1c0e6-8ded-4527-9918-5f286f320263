"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("@babel/runtime/helpers/extends"),t=require("react"),r=require("three"),n=require("./Line.cjs.js");function o(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function c(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var n=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,n.get?n:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,Object.freeze(t)}require("@react-three/fiber"),require("three-stdlib");var u=o(e),s=c(t);const i=new r.Vector3,a=s.forwardRef((function({start:e=[0,0,0],end:t=[0,0,0],mid:o,segments:c=20,...a},f){const d=s.useRef(null);s.useImperativeHandle(f,(()=>d.current));const[l]=s.useState((()=>new r.QuadraticBezierCurve3(void 0,void 0,void 0))),v=s.useCallback(((e,t,n,o=20)=>(e instanceof r.Vector3?l.v0.copy(e):l.v0.set(...e),t instanceof r.Vector3?l.v2.copy(t):l.v2.set(...t),n instanceof r.Vector3?l.v1.copy(n):Array.isArray(n)?l.v1.set(...n):l.v1.copy(l.v0.clone().add(l.v2.clone().sub(l.v0)).add(i.set(0,l.v0.y-l.v2.y,0))),l.getPoints(o))),[]);s.useLayoutEffect((()=>{d.current.setPoints=(e,t,r)=>{const n=v(e,t,r);d.current.geometry&&d.current.geometry.setPositions(n.map((e=>e.toArray())).flat())}}),[]);const y=s.useMemo((()=>v(e,t,o,c)),[e,t,o,c]);return s.createElement(n.Line,u.default({ref:d,points:y},a))}));exports.QuadraticBezierLine=a;
