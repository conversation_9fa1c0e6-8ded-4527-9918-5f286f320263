import mongoose from 'mongoose';

const ReviewSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  product: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Product',
    required: true,
  },
  rating: {
    type: Number,
    required: true,
    min: 1,
    max: 5,
  },
  title: {
    type: String,
    required: true,
    maxlength: 100,
  },
  comment: {
    type: String,
    required: true,
    maxlength: 1000,
  },
  verified: {
    type: Boolean,
    default: false, // True if user purchased the product
  },
  helpful: [{
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
    },
    helpful: Boolean, // true for helpful, false for not helpful
  }],
  status: {
    type: String,
    enum: ['pending', 'approved', 'rejected'],
    default: 'pending',
  },
  moderatorNotes: {
    type: String,
  },
  images: [{
    url: String,
    alt: String,
  }],
}, {
  timestamps: true,
});

// Compound index to prevent duplicate reviews per user per product
ReviewSchema.index({ user: 1, product: 1 }, { unique: true });

// Index for efficient queries
ReviewSchema.index({ product: 1, status: 1, createdAt: -1 });
ReviewSchema.index({ rating: 1 });
ReviewSchema.index({ status: 1 });

// Calculate helpful score
ReviewSchema.virtual('helpfulScore').get(function() {
  if (!this.helpful || this.helpful.length === 0) return 0;
  const helpfulCount = this.helpful.filter(h => h.helpful).length;
  const notHelpfulCount = this.helpful.filter(h => !h.helpful).length;
  return helpfulCount - notHelpfulCount;
});

export default mongoose.models.Review || mongoose.model('Review', ReviewSchema);
