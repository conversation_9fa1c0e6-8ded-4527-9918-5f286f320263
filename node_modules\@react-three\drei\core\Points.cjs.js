"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("@babel/runtime/helpers/extends"),t=require("three"),r=require("react"),n=require("@react-three/fiber");function a(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function s(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var n=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,n.get?n:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,Object.freeze(t)}var i=a(e),o=s(t),u=s(r);const c=new o.Matrix4,l=new o.Ray,f=new o.Sphere,d=new o.Vector3;class p extends o.Group{constructor(){super(),this.size=0,this.color=new o.Color("white"),this.instance={current:void 0},this.instanceKey={current:void 0}}get geometry(){var e;return null==(e=this.instance.current)?void 0:e.geometry}raycast(e,t){var r,n;const a=this.instance.current;if(!a||!a.geometry)return;const s=a.userData.instances.indexOf(this.instanceKey);if(-1===s||s>a.geometry.drawRange.count)return;const i=null!==(r=null==(n=e.params.Points)?void 0:n.threshold)&&void 0!==r?r:1;if(f.set(this.getWorldPosition(d),i),!1===e.ray.intersectsSphere(f))return;c.copy(a.matrixWorld).invert(),l.copy(e.ray).applyMatrix4(c);const u=i/((this.scale.x+this.scale.y+this.scale.z)/3),p=u*u,m=l.distanceSqToPoint(this.position);if(m<p){const r=new o.Vector3;l.closestPointToPoint(this.position,r),r.applyMatrix4(this.matrixWorld);const n=e.ray.origin.distanceTo(r);if(n<e.near||n>e.far)return;t.push({distance:n,distanceToRay:Math.sqrt(m),point:r,index:s,face:null,object:this})}}}let m,y;const g=u.createContext(null),h=new o.Matrix4,b=new o.Vector3,x=u.forwardRef((({children:e,range:t,limit:r=1e3,...a},s)=>{const c=u.useRef(null);u.useImperativeHandle(s,(()=>c.current),[]);const[l,f]=u.useState([]),[[d,p,x]]=u.useState((()=>[new Float32Array(3*r),Float32Array.from({length:3*r},(()=>1)),Float32Array.from({length:r},(()=>1))]));u.useEffect((()=>{c.current.geometry.attributes.position.needsUpdate=!0})),n.useFrame((()=>{for(c.current.updateMatrix(),c.current.updateMatrixWorld(),h.copy(c.current.matrixWorld).invert(),c.current.geometry.drawRange.count=Math.min(r,void 0!==t?t:r,l.length),m=0;m<l.length;m++)y=l[m].current,y.getWorldPosition(b).applyMatrix4(h),b.toArray(d,3*m),c.current.geometry.attributes.position.needsUpdate=!0,y.matrixWorldNeedsUpdate=!0,y.color.toArray(p,3*m),c.current.geometry.attributes.color.needsUpdate=!0,x.set([y.size],m),c.current.geometry.attributes.size.needsUpdate=!0}));const w=u.useMemo((()=>({getParent:()=>c,subscribe:e=>(f((t=>[...t,e])),()=>f((t=>t.filter((t=>t.current!==e.current)))))})),[]);return u.createElement("points",i.default({userData:{instances:l},matrixAutoUpdate:!1,ref:c,raycast:()=>null},a),u.createElement("bufferGeometry",null,u.createElement("bufferAttribute",{attach:"attributes-position",args:[d,3],usage:o.DynamicDrawUsage}),u.createElement("bufferAttribute",{attach:"attributes-color",args:[p,3],usage:o.DynamicDrawUsage}),u.createElement("bufferAttribute",{attach:"attributes-size",args:[x,1],usage:o.DynamicDrawUsage})),u.createElement(g.Provider,{value:w},e))})),w=u.forwardRef((({children:e,...t},r)=>{u.useMemo((()=>n.extend({PositionPoint:p})),[]);const a=u.useRef(null);u.useImperativeHandle(r,(()=>a.current),[]);const{subscribe:s,getParent:o}=u.useContext(g);return u.useLayoutEffect((()=>s(a)),[]),u.createElement("positionPoint",i.default({instance:o(),instanceKey:a,ref:a},t),e)})),v=u.forwardRef((({children:e,positions:t,colors:r,sizes:a,stride:s=3,...c},l)=>{const f=u.useRef(null);return u.useImperativeHandle(l,(()=>f.current),[]),n.useFrame((()=>{const e=f.current.geometry.attributes;e.position.needsUpdate=!0,r&&(e.color.needsUpdate=!0),a&&(e.size.needsUpdate=!0)})),u.createElement("points",i.default({ref:f},c),u.createElement("bufferGeometry",null,u.createElement("bufferAttribute",{attach:"attributes-position",args:[t,s],usage:o.DynamicDrawUsage}),r&&u.createElement("bufferAttribute",{attach:"attributes-color",args:[r,s],count:r.length/s,usage:o.DynamicDrawUsage}),a&&u.createElement("bufferAttribute",{attach:"attributes-size",args:[a,1],count:a.length/s,usage:o.DynamicDrawUsage})),e)})),P=u.forwardRef(((e,t)=>e.positions instanceof Float32Array?u.createElement(v,i.default({},e,{ref:t})):u.createElement(x,i.default({},e,{ref:t}))));exports.Point=w,exports.Points=P,exports.PointsBuffer=v,exports.PositionPoint=p;
