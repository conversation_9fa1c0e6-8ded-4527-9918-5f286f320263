import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { stripe, formatAmountForStripe } from '@/lib/stripe';
import connectDB from '@/lib/mongoose';
import Product from '@/models/Product';
import Order from '@/models/Order';

export async function POST(request) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const { items, customerInfo } = await request.json();
    
    if (!items || items.length === 0) {
      return NextResponse.json(
        { error: 'No items in cart' },
        { status: 400 }
      );
    }

    await connectDB();

    // Fetch product details and calculate total
    const productIds = items.map(item => item.id);
    const products = await Product.find({ _id: { $in: productIds } });
    
    let subtotal = 0;
    const lineItems = [];
    const orderItems = [];

    for (const item of items) {
      const product = products.find(p => p._id.toString() === item.id);
      
      if (!product) {
        return NextResponse.json(
          { error: `Product not found: ${item.id}` },
          { status: 400 }
        );
      }

      const price = product.salePrice || product.price;
      const itemTotal = price * item.quantity;
      subtotal += itemTotal;

      // Stripe line item
      lineItems.push({
        price_data: {
          currency: 'usd',
          product_data: {
            name: product.title,
            description: product.shortDescription,
            images: product.images.filter(img => img.isPrimary).map(img => img.url),
          },
          unit_amount: formatAmountForStripe(price),
        },
        quantity: item.quantity,
      });

      // Order item
      orderItems.push({
        product: product._id,
        title: product.title,
        price: price,
        quantity: item.quantity,
      });
    }

    // Create order in database
    const order = new Order({
      user: session.user.id,
      items: orderItems,
      subtotal,
      total: subtotal, // Add tax calculation if needed
      customerInfo,
      status: 'pending',
      paymentStatus: 'pending',
    });

    await order.save();

    // Create Stripe checkout session
    const checkoutSession = await stripe.checkout.sessions.create({
      payment_method_types: ['card'],
      line_items: lineItems,
      mode: 'payment',
      success_url: `${request.headers.get('origin')}/checkout/success?session_id={CHECKOUT_SESSION_ID}`,
      cancel_url: `${request.headers.get('origin')}/cart`,
      customer_email: session.user.email,
      metadata: {
        orderId: order._id.toString(),
        userId: session.user.id,
      },
    });

    // Update order with Stripe session ID
    order.stripeSessionId = checkoutSession.id;
    await order.save();

    return NextResponse.json({
      sessionId: checkoutSession.id,
      url: checkoutSession.url,
    });

  } catch (error) {
    console.error('Checkout error:', error);
    return NextResponse.json(
      { error: 'Failed to create checkout session' },
      { status: 500 }
    );
  }
}
