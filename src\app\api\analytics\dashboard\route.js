import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import connectDB from '@/lib/mongoose';
import Order from '@/models/Order';
import Product from '@/models/Product';
import User from '@/models/User';
import Review from '@/models/Review';

export async function GET(request) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session || session.user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 403 }
      );
    }

    await connectDB();
    
    const { searchParams } = new URL(request.url);
    const period = searchParams.get('period') || '30'; // days
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - parseInt(period));

    // Sales Metrics
    const salesData = await Order.aggregate([
      {
        $match: {
          createdAt: { $gte: startDate },
          status: 'completed',
        },
      },
      {
        $group: {
          _id: null,
          totalRevenue: { $sum: '$total' },
          totalOrders: { $sum: 1 },
          averageOrderValue: { $avg: '$total' },
        },
      },
    ]);

    // Daily sales for chart
    const dailySales = await Order.aggregate([
      {
        $match: {
          createdAt: { $gte: startDate },
          status: 'completed',
        },
      },
      {
        $group: {
          _id: {
            $dateToString: {
              format: '%Y-%m-%d',
              date: '$createdAt',
            },
          },
          revenue: { $sum: '$total' },
          orders: { $sum: 1 },
        },
      },
      { $sort: { _id: 1 } },
    ]);

    // Top products
    const topProducts = await Order.aggregate([
      {
        $match: {
          createdAt: { $gte: startDate },
          status: 'completed',
        },
      },
      { $unwind: '$items' },
      {
        $group: {
          _id: '$items.product',
          title: { $first: '$items.title' },
          totalSales: { $sum: { $multiply: ['$items.price', '$items.quantity'] } },
          totalQuantity: { $sum: '$items.quantity' },
        },
      },
      { $sort: { totalSales: -1 } },
      { $limit: 10 },
    ]);

    // Category performance
    const categoryPerformance = await Order.aggregate([
      {
        $match: {
          createdAt: { $gte: startDate },
          status: 'completed',
        },
      },
      { $unwind: '$items' },
      {
        $lookup: {
          from: 'products',
          localField: 'items.product',
          foreignField: '_id',
          as: 'product',
        },
      },
      { $unwind: '$product' },
      {
        $group: {
          _id: '$product.category',
          revenue: { $sum: { $multiply: ['$items.price', '$items.quantity'] } },
          orders: { $sum: 1 },
        },
      },
      { $sort: { revenue: -1 } },
    ]);

    // User metrics
    const userMetrics = await User.aggregate([
      {
        $facet: {
          totalUsers: [{ $count: 'count' }],
          newUsers: [
            {
              $match: {
                createdAt: { $gte: startDate },
              },
            },
            { $count: 'count' },
          ],
          activeUsers: [
            {
              $match: {
                'analytics.lastLoginAt': { $gte: startDate },
              },
            },
            { $count: 'count' },
          ],
        },
      },
    ]);

    // Product metrics
    const productMetrics = await Product.aggregate([
      {
        $facet: {
          totalProducts: [
            { $match: { status: 'published' } },
            { $count: 'count' },
          ],
          featuredProducts: [
            { $match: { status: 'published', featured: true } },
            { $count: 'count' },
          ],
          averageRating: [
            {
              $match: {
                status: 'published',
                'rating.count': { $gt: 0 },
              },
            },
            {
              $group: {
                _id: null,
                avgRating: { $avg: '$rating.average' },
              },
            },
          ],
        },
      },
    ]);

    // Review metrics
    const reviewMetrics = await Review.aggregate([
      {
        $facet: {
          totalReviews: [
            { $match: { status: 'approved' } },
            { $count: 'count' },
          ],
          pendingReviews: [
            { $match: { status: 'pending' } },
            { $count: 'count' },
          ],
          averageRating: [
            {
              $match: { status: 'approved' },
            },
            {
              $group: {
                _id: null,
                avgRating: { $avg: '$rating' },
              },
            },
          ],
          recentReviews: [
            {
              $match: {
                status: 'approved',
                createdAt: { $gte: startDate },
              },
            },
            { $count: 'count' },
          ],
        },
      },
    ]);

    // Conversion metrics (mock data - would need proper tracking)
    const conversionMetrics = {
      visitorsToSignup: 0.15, // 15%
      signupToPurchase: 0.25, // 25%
      cartAbandonment: 0.68, // 68%
      repeatPurchaseRate: 0.32, // 32%
    };

    // Compile response
    const analytics = {
      sales: {
        totalRevenue: salesData[0]?.totalRevenue || 0,
        totalOrders: salesData[0]?.totalOrders || 0,
        averageOrderValue: salesData[0]?.averageOrderValue || 0,
        dailySales,
      },
      products: {
        total: productMetrics[0]?.totalProducts[0]?.count || 0,
        featured: productMetrics[0]?.featuredProducts[0]?.count || 0,
        averageRating: productMetrics[0]?.averageRating[0]?.avgRating || 0,
        topProducts,
        categoryPerformance,
      },
      users: {
        total: userMetrics[0]?.totalUsers[0]?.count || 0,
        new: userMetrics[0]?.newUsers[0]?.count || 0,
        active: userMetrics[0]?.activeUsers[0]?.count || 0,
      },
      reviews: {
        total: reviewMetrics[0]?.totalReviews[0]?.count || 0,
        pending: reviewMetrics[0]?.pendingReviews[0]?.count || 0,
        recent: reviewMetrics[0]?.recentReviews[0]?.count || 0,
        averageRating: reviewMetrics[0]?.averageRating[0]?.avgRating || 0,
      },
      conversion: conversionMetrics,
      period: parseInt(period),
    };

    return NextResponse.json(analytics);
  } catch (error) {
    console.error('Error fetching analytics:', error);
    return NextResponse.json(
      { error: 'Failed to fetch analytics' },
      { status: 500 }
    );
  }
}
