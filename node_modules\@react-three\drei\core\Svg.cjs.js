"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("@babel/runtime/helpers/extends"),t=require("@react-three/fiber"),r=require("react"),a=require("three"),s=require("three-stdlib");function l(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function o(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var a=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,a.get?a:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,Object.freeze(t)}var i=l(e),u=o(r);const n=r.forwardRef((function({src:e,skipFill:l,skipStrokes:o,fillMaterial:n,strokeMaterial:c,fillMeshProps:d,strokeMeshProps:f,...p},m){const y=t.useLoader(s.SVGLoader,e.startsWith("<svg")?`data:image/svg+xml;utf8,${e}`:e),h=r.useMemo((()=>o?[]:y.paths.map((e=>{var t;return void 0===(null==(t=e.userData)?void 0:t.style.stroke)||"none"===e.userData.style.stroke?null:e.subPaths.map((t=>s.SVGLoader.pointsToStroke(t.getPoints(),e.userData.style)))}))),[y,o]);r.useEffect((()=>()=>h.forEach((e=>e&&e.map((e=>e.dispose()))))),[h]);let b=0;return u.createElement("object3D",i.default({ref:m},p),u.createElement("object3D",{scale:[1,-1,1]},y.paths.map(((e,t)=>{var p,m;return u.createElement(r.Fragment,{key:t},!l&&void 0!==(null==(p=e.userData)?void 0:p.style.fill)&&"none"!==e.userData.style.fill&&s.SVGLoader.createShapes(e).map(((t,r)=>u.createElement("mesh",i.default({key:r},d,{renderOrder:b++}),u.createElement("shapeGeometry",{args:[t]}),u.createElement("meshBasicMaterial",i.default({color:e.userData.style.fill,opacity:e.userData.style.fillOpacity,transparent:!0,side:a.DoubleSide,depthWrite:!1},n))))),!o&&void 0!==(null==(m=e.userData)?void 0:m.style.stroke)&&"none"!==e.userData.style.stroke&&e.subPaths.map(((r,s)=>u.createElement("mesh",i.default({key:s,geometry:h[t][s]},f,{renderOrder:b++}),u.createElement("meshBasicMaterial",i.default({color:e.userData.style.stroke,opacity:e.userData.style.strokeOpacity,transparent:!0,side:a.DoubleSide,depthWrite:!1},c))))))}))))}));exports.Svg=n;
