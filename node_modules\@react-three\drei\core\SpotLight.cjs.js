"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("@babel/runtime/helpers/extends"),t=require("react"),r=require("three"),a=require("@react-three/fiber"),n=require("three-stdlib"),o=require("../materials/SpotLightMaterial.cjs.js");function i(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function u(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var a=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,a.get?a:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,Object.freeze(t)}require("../helpers/constants.cjs.js");var l=i(e),s=u(t);function c({opacity:e=1,radiusTop:t,radiusBottom:n,depthBuffer:i,color:u="white",distance:l=5,angle:c=.15,attenuation:p=5,anglePower:d=5}){const m=s.useRef(null),h=a.useThree((e=>e.size)),f=a.useThree((e=>e.camera)),g=a.useThree((e=>e.viewport.dpr)),[v]=s.useState((()=>new o.SpotLightMaterial)),[w]=s.useState((()=>new r.Vector3));t=void 0===t?.1:t,n=void 0===n?7*c:n,a.useFrame((()=>{v.uniforms.spotPosition.value.copy(m.current.getWorldPosition(w)),m.current.lookAt(m.current.parent.target.getWorldPosition(w))}));const S=s.useMemo((()=>{const e=new r.CylinderGeometry(t,n,l,128,64,!0);return e.applyMatrix4((new r.Matrix4).makeTranslation(0,-l/2,0)),e.applyMatrix4((new r.Matrix4).makeRotationX(-Math.PI/2)),e}),[l,t,n]);return s.createElement(s.Fragment,null,s.createElement("mesh",{ref:m,geometry:S,raycast:()=>null},s.createElement("primitive",{object:v,attach:"material","uniforms-opacity-value":e,"uniforms-lightColor-value":u,"uniforms-attenuation-value":p,"uniforms-anglePower-value":d,"uniforms-depth-value":i,"uniforms-cameraNear-value":f.near,"uniforms-cameraFar-value":f.far,"uniforms-resolution-value":i?[h.width*g,h.height*g]:[0,0]})))}function p(e,t,n,o,i){const[[u,l]]=s.useState((()=>[new r.Vector3,new r.Vector3]));s.useLayoutEffect((()=>{if(!(null==(t=e.current)?void 0:t.isSpotLight))throw new Error("SpotlightShadow must be a child of a SpotLight");var t;e.current.shadow.mapSize.set(n,o),e.current.shadow.needsUpdate=!0}),[e,n,o]),a.useFrame((()=>{if(!e.current)return;const r=e.current.position,a=e.current.target.position;l.copy(a).sub(r);var n=l.length();l.normalize().multiplyScalar(n*i),u.copy(r).add(l),t.current.position.copy(u),t.current.lookAt(e.current.target.position)}))}function d({distance:e=.4,alphaTest:t=.5,map:o,shader:i="#define GLSLIFY 1\nvarying vec2 vUv;uniform sampler2D uShadowMap;uniform float uTime;void main(){vec3 color=texture2D(uShadowMap,vUv).xyz;gl_FragColor=vec4(color,1.);}",width:u=512,height:l=512,scale:c=1,children:d,...m}){const h=s.useRef(null),f=m.spotlightRef,g=m.debug;p(f,h,u,l,e);const v=s.useMemo((()=>new r.WebGLRenderTarget(u,l,{format:r.RGBAFormat,stencilBuffer:!1})),[u,l]),w=s.useRef({uShadowMap:{value:o},uTime:{value:0}});s.useEffect((()=>{w.current.uShadowMap.value=o}),[o]);const S=s.useMemo((()=>new n.FullScreenQuad(new r.ShaderMaterial({uniforms:w.current,vertexShader:"\n          varying vec2 vUv;\n\n          void main() {\n            vUv = uv;\n            gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);\n          }\n          ",fragmentShader:i}))),[i]);return s.useEffect((()=>()=>{S.material.dispose(),S.dispose()}),[S]),s.useEffect((()=>()=>v.dispose()),[v]),a.useFrame((({gl:e},t)=>{w.current.uTime.value+=t,e.setRenderTarget(v),S.render(e),e.setRenderTarget(null)})),s.createElement(s.Fragment,null,s.createElement("mesh",{ref:h,scale:c,castShadow:!0},s.createElement("planeGeometry",null),s.createElement("meshBasicMaterial",{transparent:!0,side:r.DoubleSide,alphaTest:t,alphaMap:v.texture,"alphaMap-wrapS":r.RepeatWrapping,"alphaMap-wrapT":r.RepeatWrapping,opacity:g?1:0},d)))}function m({distance:e=.4,alphaTest:t=.5,map:a,width:n=512,height:o=512,scale:i,children:u,...l}){const c=s.useRef(null),d=l.spotlightRef,m=l.debug;return p(d,c,n,o,e),s.createElement(s.Fragment,null,s.createElement("mesh",{ref:c,scale:i,castShadow:!0},s.createElement("planeGeometry",null),s.createElement("meshBasicMaterial",{transparent:!0,side:r.DoubleSide,alphaTest:t,alphaMap:a,"alphaMap-wrapS":r.RepeatWrapping,"alphaMap-wrapT":r.RepeatWrapping,opacity:m?1:0},u)))}const h=s.forwardRef((({opacity:e=1,radiusTop:t,radiusBottom:r,depthBuffer:a,color:n="white",distance:o=5,angle:i=.15,attenuation:u=5,anglePower:p=5,volumetric:d=!0,debug:m=!1,children:h,...f},g)=>{const v=s.useRef(null);return s.useImperativeHandle(g,(()=>v.current),[]),s.createElement("group",null,m&&v.current&&s.createElement("spotLightHelper",{args:[v.current]}),s.createElement("spotLight",l.default({ref:v,angle:i,color:n,distance:o,castShadow:!0},f),d&&s.createElement(c,{debug:m,opacity:e,radiusTop:t,radiusBottom:r,depthBuffer:a,color:n,distance:o,angle:i,attenuation:u,anglePower:p})),h&&s.cloneElement(h,{spotlightRef:v,debug:m}))}));exports.SpotLight=h,exports.SpotLightShadow=function(e){return e.shader?s.createElement(d,e):s.createElement(m,e)};
