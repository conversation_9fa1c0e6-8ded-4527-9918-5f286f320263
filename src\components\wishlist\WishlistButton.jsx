'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { toast } from '@/components/ui/Toaster';
import { HeartIcon } from '@heroicons/react/24/outline';
import { HeartIcon as HeartSolidIcon } from '@heroicons/react/24/solid';

/**
 * Wishlist toggle button component
 * @param {Object} props - Component props
 * @param {string} props.productId - ID of the product
 * @param {string} props.size - Size of the heart icon ('sm', 'md', 'lg', 'xl')
 * @param {boolean} props.showText - Whether to show text label
 * @param {string} props.className - Additional CSS classes
 * @param {Function} props.onToggle - Callback when wishlist status changes
 * @returns {JSX.Element} Wishlist button component
 */
export default function WishlistButton({
  productId,
  size = 'md',
  showText = false,
  className = '',
  onToggle
}) {
  const { data: session } = useSession();
  const [inWishlist, setInWishlist] = useState(false);
  const [loading, setLoading] = useState(false);

  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-5 w-5',
    lg: 'h-6 w-6',
    xl: 'h-8 w-8',
  };

  useEffect(() => {
    if (session && productId) {
      checkWishlistStatus();
    }
  }, [session, productId]);

  const checkWishlistStatus = async () => {
    try {
      const response = await fetch(`/api/wishlist/${productId}`);
      if (response.ok) {
        const data = await response.json();
        setInWishlist(data.inWishlist);
      }
    } catch (error) {
      console.error('Error checking wishlist status:', error);
    }
  };

  const handleToggle = async () => {
    if (!session) {
      toast.error('Please sign in to add items to your wishlist');
      return;
    }

    setLoading(true);

    try {
      if (inWishlist) {
        // Remove from wishlist
        const response = await fetch(`/api/wishlist/${productId}`, {
          method: 'DELETE',
        });

        if (response.ok) {
          setInWishlist(false);
          toast.success('Removed from wishlist');
          if (onToggle) onToggle(false);
        } else {
          const error = await response.json();
          toast.error(error.error || 'Failed to remove from wishlist');
        }
      } else {
        // Add to wishlist
        const response = await fetch('/api/wishlist', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ productId }),
        });

        if (response.ok) {
          setInWishlist(true);
          toast.success('Added to wishlist');
          if (onToggle) onToggle(true);
        } else {
          const error = await response.json();
          toast.error(error.error || 'Failed to add to wishlist');
        }
      }
    } catch (error) {
      console.error('Error toggling wishlist:', error);
      toast.error('Something went wrong');
    } finally {
      setLoading(false);
    }
  };

  return (
    <button
      onClick={handleToggle}
      disabled={loading || !session}
      className={`
        flex items-center justify-center transition-all duration-200
        ${inWishlist
          ? 'text-red-500 hover:text-red-600'
          : 'text-gray-400 hover:text-red-500'
        }
        ${loading ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
        ${!session ? 'opacity-50 cursor-not-allowed' : ''}
        ${showText ? 'space-x-2' : ''}
        ${className}
      `}
      title={
        !session
          ? 'Sign in to add to wishlist'
          : inWishlist
            ? 'Remove from wishlist'
            : 'Add to wishlist'
      }
    >
      {inWishlist ? (
        <HeartSolidIcon
          className={`${sizeClasses[size]} ${loading ? 'animate-pulse' : ''}`}
        />
      ) : (
        <HeartIcon
          className={`${sizeClasses[size]} ${loading ? 'animate-pulse' : ''}`}
        />
      )}

      {showText && (
        <span className="text-sm font-medium">
          {loading
            ? 'Loading...'
            : inWishlist
              ? 'In Wishlist'
              : 'Add to Wishlist'
          }
        </span>
      )}
    </button>
  );
}

// Compact version for product cards
export function WishlistIcon({ productId, className = '' }) {
  return (
    <WishlistButton
      productId={productId}
      size="md"
      className={`p-2 rounded-full bg-white shadow-sm hover:shadow-md ${className}`}
    />
  );
}

// Full button version
export function WishlistFullButton({ productId, className = '' }) {
  return (
    <WishlistButton
      productId={productId}
      size="md"
      showText={true}
      className={`px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 ${className}`}
    />
  );
}
