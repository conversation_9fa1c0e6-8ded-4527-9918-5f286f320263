import mongoose from 'mongoose';

const WishlistSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  name: {
    type: String,
    default: 'My Wishlist',
    maxlength: 100,
  },
  description: {
    type: String,
    maxlength: 500,
  },
  items: [{
    product: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Product',
      required: true,
    },
    addedAt: {
      type: Date,
      default: Date.now,
    },
    notes: {
      type: String,
      maxlength: 200,
    },
  }],
  isPublic: {
    type: Boolean,
    default: false,
  },
  shareToken: {
    type: String,
    unique: true,
    sparse: true, // Only create index for non-null values
  },
  sharedAt: {
    type: Date,
  },
  emailNotifications: {
    priceDrops: {
      type: Boolean,
      default: true,
    },
    backInStock: {
      type: Boolean,
      default: true,
    },
    newSimilarProducts: {
      type: Boolean,
      default: false,
    },
  },
}, {
  timestamps: true,
});

// Indexes for efficient queries
WishlistSchema.index({ user: 1 });
WishlistSchema.index({ shareToken: 1 });
WishlistSchema.index({ 'items.product': 1 });

// Generate share token when making wishlist public
WishlistSchema.pre('save', function(next) {
  if (this.isPublic && !this.shareToken) {
    this.shareToken = generateShareToken();
    this.sharedAt = new Date();
  } else if (!this.isPublic) {
    this.shareToken = undefined;
    this.sharedAt = undefined;
  }
  next();
});

// Virtual for item count
WishlistSchema.virtual('itemCount').get(function() {
  return this.items ? this.items.length : 0;
});

// Method to add item to wishlist
WishlistSchema.methods.addItem = function(productId, notes = '') {
  const existingItem = this.items.find(item => 
    item.product.toString() === productId.toString()
  );
  
  if (!existingItem) {
    this.items.push({
      product: productId,
      notes,
      addedAt: new Date(),
    });
  }
  
  return this.save();
};

// Method to remove item from wishlist
WishlistSchema.methods.removeItem = function(productId) {
  this.items = this.items.filter(item => 
    item.product.toString() !== productId.toString()
  );
  
  return this.save();
};

// Method to check if product is in wishlist
WishlistSchema.methods.hasProduct = function(productId) {
  return this.items.some(item => 
    item.product.toString() === productId.toString()
  );
};

function generateShareToken() {
  return Math.random().toString(36).substring(2, 15) + 
         Math.random().toString(36).substring(2, 15);
}

export default mongoose.models.Wishlist || mongoose.model('Wishlist', WishlistSchema);
