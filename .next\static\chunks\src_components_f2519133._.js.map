{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/coding/luyari/luyaridesignstemplate/src/components/layout/Header.jsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport Link from 'next/link';\nimport { useSession, signIn, signOut } from 'next-auth/react';\nimport { useCart } from '@/components/providers/CartProvider';\nimport {\n  ShoppingCartIcon,\n  UserIcon,\n  Bars3Icon,\n  XMarkIcon,\n  MagnifyingGlassIcon,\n  HeartIcon\n} from '@heroicons/react/24/outline';\n\nexport default function Header() {\n  const { data: session } = useSession();\n  const { getCartCount } = useCart();\n  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);\n  const [wishlistCount, setWishlistCount] = useState(0);\n  const cartCount = getCartCount();\n\n  useEffect(() => {\n    if (session) {\n      fetchWishlistCount();\n    }\n  }, [session]);\n\n  const fetchWishlistCount = async () => {\n    try {\n      const response = await fetch('/api/wishlist');\n      if (response.ok) {\n        const data = await response.json();\n        setWishlistCount(data.itemCount || 0);\n      }\n    } catch (error) {\n      console.error('Error fetching wishlist count:', error);\n    }\n  };\n\n  const navigation = [\n    { name: 'Shop', href: '/shop' },\n    { name: 'Collections', href: '/collections' },\n    { name: 'House Plans', href: '/collections/house-plans' },\n    { name: 'Templates', href: '/collections/templates' },\n    { name: 'About', href: '/about' },\n    { name: 'Contact', href: '/contact' },\n  ];\n\n  return (\n    <header className=\"bg-white shadow-sm border-b\">\n      <nav className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex h-16 justify-between items-center\">\n          {/* Logo */}\n          <div className=\"flex items-center\">\n            <Link href=\"/\" className=\"flex items-center space-x-2\">\n              <div className=\"w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center\">\n                <span className=\"text-white font-bold text-sm\">LD</span>\n              </div>\n              <span className=\"text-xl font-bold text-gray-900\">Luyari Designs</span>\n            </Link>\n          </div>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden md:flex items-center space-x-8\">\n            {navigation.map((item) => (\n              <Link\n                key={item.name}\n                href={item.href}\n                className=\"text-gray-700 hover:text-blue-600 px-3 py-2 text-sm font-medium transition-colors\"\n              >\n                {item.name}\n              </Link>\n            ))}\n          </div>\n\n          {/* Right side icons */}\n          <div className=\"flex items-center space-x-4\">\n            {/* Search */}\n            <button className=\"p-2 text-gray-600 hover:text-blue-600 transition-colors\">\n              <MagnifyingGlassIcon className=\"h-5 w-5\" />\n            </button>\n\n            {/* Wishlist */}\n            <Link href=\"/wishlist\" className=\"relative p-2 text-gray-600 hover:text-blue-600 transition-colors\">\n              <HeartIcon className=\"h-5 w-5\" />\n              {wishlistCount > 0 && (\n                <span className=\"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center\">\n                  {wishlistCount}\n                </span>\n              )}\n            </Link>\n\n            {/* Cart */}\n            <Link href=\"/cart\" className=\"relative p-2 text-gray-600 hover:text-blue-600 transition-colors\">\n              <ShoppingCartIcon className=\"h-5 w-5\" />\n              {cartCount > 0 && (\n                <span className=\"absolute -top-1 -right-1 bg-blue-600 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center\">\n                  {cartCount}\n                </span>\n              )}\n            </Link>\n\n            {/* User Menu */}\n            {session ? (\n              <div className=\"relative\">\n                <button className=\"flex items-center space-x-2 p-2 text-gray-600 hover:text-blue-600 transition-colors\">\n                  {session.user.image ? (\n                    <img\n                      src={session.user.image}\n                      alt={session.user.name}\n                      className=\"h-6 w-6 rounded-full\"\n                    />\n                  ) : (\n                    <UserIcon className=\"h-5 w-5\" />\n                  )}\n                  <span className=\"hidden sm:block text-sm\">{session.user.name}</span>\n                </button>\n                {/* Dropdown menu would go here */}\n              </div>\n            ) : (\n              <button\n                onClick={() => signIn()}\n                className=\"bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors\"\n              >\n                Sign In\n              </button>\n            )}\n\n            {/* Mobile menu button */}\n            <button\n              className=\"md:hidden p-2 text-gray-600 hover:text-blue-600 transition-colors\"\n              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}\n            >\n              {mobileMenuOpen ? (\n                <XMarkIcon className=\"h-5 w-5\" />\n              ) : (\n                <Bars3Icon className=\"h-5 w-5\" />\n              )}\n            </button>\n          </div>\n        </div>\n\n        {/* Mobile Navigation */}\n        {mobileMenuOpen && (\n          <div className=\"md:hidden border-t border-gray-200 py-4\">\n            <div className=\"space-y-2\">\n              {navigation.map((item) => (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  className=\"block px-3 py-2 text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-lg transition-colors\"\n                  onClick={() => setMobileMenuOpen(false)}\n                >\n                  {item.name}\n                </Link>\n              ))}\n              {!session && (\n                <button\n                  onClick={() => {\n                    signIn();\n                    setMobileMenuOpen(false);\n                  }}\n                  className=\"block w-full text-left px-3 py-2 text-blue-600 font-medium hover:bg-gray-50 rounded-lg transition-colors\"\n                >\n                  Sign In\n                </button>\n              )}\n            </div>\n          </div>\n        )}\n      </nav>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AANA;;;;;;AAee,SAAS;;IACtB,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,wIAAA,CAAA,aAAU,AAAD;IACnC,MAAM,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,kJAAA,CAAA,UAAO,AAAD;IAC/B,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,YAAY;IAElB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,IAAI,SAAS;gBACX;YACF;QACF;2BAAG;QAAC;KAAQ;IAEZ,MAAM,qBAAqB;QACzB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,iBAAiB,KAAK,SAAS,IAAI;YACrC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;QAClD;IACF;IAEA,MAAM,aAAa;QACjB;YAAE,MAAM;YAAQ,MAAM;QAAQ;QAC9B;YAAE,MAAM;YAAe,MAAM;QAAe;QAC5C;YAAE,MAAM;YAAe,MAAM;QAA2B;QACxD;YAAE,MAAM;YAAa,MAAM;QAAyB;QACpD;YAAE,MAAM;YAAS,MAAM;QAAS;QAChC;YAAE,MAAM;YAAW,MAAM;QAAW;KACrC;IAED,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;;kDACvB,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAK,WAAU;sDAA+B;;;;;;;;;;;kDAEjD,6LAAC;wCAAK,WAAU;kDAAkC;;;;;;;;;;;;;;;;;sCAKtD,6LAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC,+JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAU;8CAET,KAAK,IAAI;mCAJL,KAAK,IAAI;;;;;;;;;;sCAUpB,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAO,WAAU;8CAChB,cAAA,6LAAC,wOAAA,CAAA,sBAAmB;wCAAC,WAAU;;;;;;;;;;;8CAIjC,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAY,WAAU;;sDAC/B,6LAAC,oNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;wCACpB,gBAAgB,mBACf,6LAAC;4CAAK,WAAU;sDACb;;;;;;;;;;;;8CAMP,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAQ,WAAU;;sDAC3B,6LAAC,kOAAA,CAAA,mBAAgB;4CAAC,WAAU;;;;;;wCAC3B,YAAY,mBACX,6LAAC;4CAAK,WAAU;sDACb;;;;;;;;;;;;gCAMN,wBACC,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAO,WAAU;;4CACf,QAAQ,IAAI,CAAC,KAAK,iBACjB,6LAAC;gDACC,KAAK,QAAQ,IAAI,CAAC,KAAK;gDACvB,KAAK,QAAQ,IAAI,CAAC,IAAI;gDACtB,WAAU;;;;;qEAGZ,6LAAC,kNAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DAEtB,6LAAC;gDAAK,WAAU;0DAA2B,QAAQ,IAAI,CAAC,IAAI;;;;;;;;;;;;;;;;yDAKhE,6LAAC;oCACC,SAAS,IAAM,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD;oCACpB,WAAU;8CACX;;;;;;8CAMH,6LAAC;oCACC,WAAU;oCACV,SAAS,IAAM,kBAAkB,CAAC;8CAEjC,+BACC,6LAAC,oNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;6DAErB,6LAAC,oNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;gBAO5B,gCACC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;4BACZ,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC,+JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAU;oCACV,SAAS,IAAM,kBAAkB;8CAEhC,KAAK,IAAI;mCALL,KAAK,IAAI;;;;;4BAQjB,CAAC,yBACA,6LAAC;gCACC,SAAS;oCACP,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD;oCACL,kBAAkB;gCACpB;gCACA,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjB;GA/JwB;;QACI,wIAAA,CAAA,aAAU;QACX,kJAAA,CAAA,UAAO;;;KAFV", "debugId": null}}, {"offset": {"line": 361, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/coding/luyari/luyaridesignstemplate/src/components/home/<USER>"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport { ArrowRightIcon } from '@heroicons/react/24/outline';\n\nexport default function Hero() {\n  return (\n    <section className=\"relative bg-gradient-to-br from-blue-50 to-purple-50 py-20 lg:py-32\">\n      <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\">\n          {/* Left Content */}\n          <div className=\"text-center lg:text-left\">\n            <h1 className=\"text-4xl sm:text-5xl lg:text-6xl font-bold text-gray-900 leading-tight\">\n              Premium Design\n              <span className=\"block text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600\">\n                Templates\n              </span>\n              for Creators\n            </h1>\n            <p className=\"mt-6 text-lg text-gray-600 max-w-2xl\">\n              Discover thousands of high-quality design templates, house plans, and digital assets \n              to bring your creative vision to life. From modern house plans to stunning UI kits.\n            </p>\n            <div className=\"mt-8 flex flex-col sm:flex-row gap-4 justify-center lg:justify-start\">\n              <Link\n                href=\"/shop\"\n                className=\"inline-flex items-center justify-center px-8 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors\"\n              >\n                Browse Templates\n                <ArrowRightIcon className=\"ml-2 h-5 w-5\" />\n              </Link>\n              <Link\n                href=\"/collections/house-plans\"\n                className=\"inline-flex items-center justify-center px-8 py-3 border border-gray-300 text-gray-700 font-medium rounded-lg hover:bg-gray-50 transition-colors\"\n              >\n                House Plans\n              </Link>\n            </div>\n            \n            {/* Stats */}\n            <div className=\"mt-12 grid grid-cols-3 gap-8 text-center lg:text-left\">\n              <div>\n                <div className=\"text-2xl font-bold text-gray-900\">5,000+</div>\n                <div className=\"text-sm text-gray-600\">Design Templates</div>\n              </div>\n              <div>\n                <div className=\"text-2xl font-bold text-gray-900\">50k+</div>\n                <div className=\"text-sm text-gray-600\">Happy Customers</div>\n              </div>\n              <div>\n                <div className=\"text-2xl font-bold text-gray-900\">4.9★</div>\n                <div className=\"text-sm text-gray-600\">Customer Rating</div>\n              </div>\n            </div>\n          </div>\n\n          {/* Right Content - Hero Image */}\n          <div className=\"relative\">\n            <div className=\"relative z-10\">\n              <img\n                src=\"/api/placeholder/600/400\"\n                alt=\"Design Templates Preview\"\n                className=\"w-full h-auto rounded-2xl shadow-2xl\"\n              />\n            </div>\n            {/* Background decorations */}\n            <div className=\"absolute -top-4 -right-4 w-72 h-72 bg-gradient-to-br from-blue-400 to-purple-400 rounded-full opacity-20 blur-3xl\"></div>\n            <div className=\"absolute -bottom-8 -left-8 w-64 h-64 bg-gradient-to-br from-purple-400 to-pink-400 rounded-full opacity-20 blur-3xl\"></div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKe,SAAS;IACtB,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;;oCAAyE;kDAErF,6LAAC;wCAAK,WAAU;kDAAmF;;;;;;oCAE5F;;;;;;;0CAGT,6LAAC;gCAAE,WAAU;0CAAuC;;;;;;0CAIpD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;;4CACX;0DAEC,6LAAC,8NAAA,CAAA,iBAAc;gDAAC,WAAU;;;;;;;;;;;;kDAE5B,6LAAC,+<PERSON><PERSON><PERSON>,CAAA,UAAI;wCA<PERSON>,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;0CAMH,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAI,WAAU;0DAAmC;;;;;;0DAClD,6LAAC;gDAAI,WAAU;0DAAwB;;;;;;;;;;;;kDAEzC,6LAAC;;0DACC,6LAAC;gDAAI,WAAU;0DAAmC;;;;;;0DAClD,6LAAC;gDAAI,WAAU;0DAAwB;;;;;;;;;;;;kDAEzC,6LAAC;;0DACC,6LAAC;gDAAI,WAAU;0DAAmC;;;;;;0DAClD,6LAAC;gDAAI,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;;;;;;;kCAM7C,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,KAAI;oCACJ,KAAI;oCACJ,WAAU;;;;;;;;;;;0CAId,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM3B;KApEwB", "debugId": null}}, {"offset": {"line": 600, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/coding/luyari/luyaridesignstemplate/src/components/home/<USER>"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport Link from 'next/link';\nimport { useCart } from '@/components/providers/CartProvider';\nimport { toast } from '@/components/ui/Toaster';\nimport { ShoppingCartIcon, EyeIcon } from '@heroicons/react/24/outline';\n\nexport default function FeaturedProducts() {\n  const [products, setProducts] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const { addItem } = useCart();\n\n  useEffect(() => {\n    // Mock data for now - replace with actual API call\n    const mockProducts = [\n      {\n        id: '1',\n        title: 'Modern 4-Bedroom House Plan',\n        shortDescription: 'Contemporary design with open floor plan',\n        price: 299,\n        salePrice: 199,\n        category: 'house-plans',\n        images: [{ url: '/api/placeholder/400/300', alt: 'Modern House Plan', isPrimary: true }],\n        specifications: { bedrooms: 4, bathrooms: 3, area: { value: 250, unit: 'sqm' } },\n        featured: true,\n        bestseller: true,\n      },\n      {\n        id: '2',\n        title: 'UI Kit - Dashboard Design',\n        shortDescription: 'Complete dashboard UI components',\n        price: 89,\n        category: 'ui-kits',\n        images: [{ url: '/api/placeholder/400/300', alt: 'UI Kit', isPrimary: true }],\n        featured: true,\n      },\n      {\n        id: '3',\n        title: 'Luxury Villa Blueprint',\n        shortDescription: '5-bedroom luxury villa with pool',\n        price: 599,\n        salePrice: 449,\n        category: 'house-plans',\n        images: [{ url: '/api/placeholder/400/300', alt: 'Luxury Villa', isPrimary: true }],\n        specifications: { bedrooms: 5, bathrooms: 4, area: { value: 450, unit: 'sqm' } },\n        featured: true,\n      },\n      {\n        id: '4',\n        title: 'Brand Identity Template',\n        shortDescription: 'Complete branding package',\n        price: 149,\n        category: 'templates',\n        images: [{ url: '/api/placeholder/400/300', alt: 'Brand Identity', isPrimary: true }],\n        featured: true,\n      },\n    ];\n\n    setTimeout(() => {\n      setProducts(mockProducts);\n      setLoading(false);\n    }, 1000);\n  }, []);\n\n  const handleAddToCart = (product) => {\n    addItem({\n      id: product.id,\n      title: product.title,\n      price: product.price,\n      salePrice: product.salePrice,\n      image: product.images[0]?.url,\n    });\n    toast.success('Added to cart!');\n  };\n\n  if (loading) {\n    return (\n      <section className=\"py-16 bg-white\">\n        <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-12\">\n            <h2 className=\"text-3xl font-bold text-gray-900\">Featured Products</h2>\n          </div>\n          <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6\">\n            {[...Array(4)].map((_, i) => (\n              <div key={i} className=\"animate-pulse\">\n                <div className=\"bg-gray-200 h-48 rounded-lg mb-4\"></div>\n                <div className=\"h-4 bg-gray-200 rounded mb-2\"></div>\n                <div className=\"h-4 bg-gray-200 rounded w-3/4\"></div>\n              </div>\n            ))}\n          </div>\n        </div>\n      </section>\n    );\n  }\n\n  return (\n    <section className=\"py-16 bg-white\">\n      <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n        <div className=\"text-center mb-12\">\n          <h2 className=\"text-3xl font-bold text-gray-900 mb-4\">Featured Products</h2>\n          <p className=\"text-lg text-gray-600 max-w-2xl mx-auto\">\n            Discover our most popular and highest-rated design templates\n          </p>\n        </div>\n\n        <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6\">\n          {products.map((product) => (\n            <div key={product.id} className=\"group relative bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-lg transition-shadow\">\n              {/* Product Image */}\n              <div className=\"relative overflow-hidden rounded-t-lg\">\n                <img\n                  src={product.images[0]?.url}\n                  alt={product.images[0]?.alt}\n                  className=\"w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300\"\n                />\n                {product.bestseller && (\n                  <span className=\"absolute top-2 left-2 bg-red-500 text-white text-xs px-2 py-1 rounded\">\n                    Bestseller\n                  </span>\n                )}\n                {product.salePrice && (\n                  <span className=\"absolute top-2 right-2 bg-green-500 text-white text-xs px-2 py-1 rounded\">\n                    Sale\n                  </span>\n                )}\n                \n                {/* Hover Actions */}\n                <div className=\"absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center space-x-2\">\n                  <Link\n                    href={`/products/${product.id}`}\n                    className=\"bg-white text-gray-900 p-2 rounded-full hover:bg-gray-100 transition-colors\"\n                  >\n                    <EyeIcon className=\"h-5 w-5\" />\n                  </Link>\n                  <button\n                    onClick={() => handleAddToCart(product)}\n                    className=\"bg-blue-600 text-white p-2 rounded-full hover:bg-blue-700 transition-colors\"\n                  >\n                    <ShoppingCartIcon className=\"h-5 w-5\" />\n                  </button>\n                </div>\n              </div>\n\n              {/* Product Info */}\n              <div className=\"p-4\">\n                <h3 className=\"font-semibold text-gray-900 mb-2 line-clamp-2\">\n                  {product.title}\n                </h3>\n                <p className=\"text-sm text-gray-600 mb-3 line-clamp-2\">\n                  {product.shortDescription}\n                </p>\n                \n                {/* Specifications for house plans */}\n                {product.category === 'house-plans' && product.specifications && (\n                  <div className=\"flex text-xs text-gray-500 space-x-3 mb-3\">\n                    {product.specifications.bedrooms && (\n                      <span>{product.specifications.bedrooms} bed</span>\n                    )}\n                    {product.specifications.bathrooms && (\n                      <span>{product.specifications.bathrooms} bath</span>\n                    )}\n                    {product.specifications.area && (\n                      <span>{product.specifications.area.value} {product.specifications.area.unit}</span>\n                    )}\n                  </div>\n                )}\n\n                {/* Price */}\n                <div className=\"flex items-center justify-between\">\n                  <div className=\"flex items-center space-x-2\">\n                    {product.salePrice ? (\n                      <>\n                        <span className=\"text-lg font-bold text-gray-900\">\n                          ${product.salePrice}\n                        </span>\n                        <span className=\"text-sm text-gray-500 line-through\">\n                          ${product.price}\n                        </span>\n                      </>\n                    ) : (\n                      <span className=\"text-lg font-bold text-gray-900\">\n                        ${product.price}\n                      </span>\n                    )}\n                  </div>\n                  <button\n                    onClick={() => handleAddToCart(product)}\n                    className=\"text-blue-600 hover:text-blue-700 transition-colors\"\n                  >\n                    <ShoppingCartIcon className=\"h-5 w-5\" />\n                  </button>\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n\n        <div className=\"text-center mt-12\">\n          <Link\n            href=\"/shop\"\n            className=\"inline-flex items-center px-6 py-3 border border-gray-300 text-gray-700 font-medium rounded-lg hover:bg-gray-50 transition-colors\"\n          >\n            View All Products\n          </Link>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;;;AANA;;;;;;AAQe,SAAS;;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IAC3C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,kJAAA,CAAA,UAAO,AAAD;IAE1B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,mDAAmD;YACnD,MAAM,eAAe;gBACnB;oBACE,IAAI;oBACJ,OAAO;oBACP,kBAAkB;oBAClB,OAAO;oBACP,WAAW;oBACX,UAAU;oBACV,QAAQ;wBAAC;4BAAE,KAAK;4BAA4B,KAAK;4BAAqB,WAAW;wBAAK;qBAAE;oBACxF,gBAAgB;wBAAE,UAAU;wBAAG,WAAW;wBAAG,MAAM;4BAAE,OAAO;4BAAK,MAAM;wBAAM;oBAAE;oBAC/E,UAAU;oBACV,YAAY;gBACd;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,kBAAkB;oBAClB,OAAO;oBACP,UAAU;oBACV,QAAQ;wBAAC;4BAAE,KAAK;4BAA4B,KAAK;4BAAU,WAAW;wBAAK;qBAAE;oBAC7E,UAAU;gBACZ;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,kBAAkB;oBAClB,OAAO;oBACP,WAAW;oBACX,UAAU;oBACV,QAAQ;wBAAC;4BAAE,KAAK;4BAA4B,KAAK;4BAAgB,WAAW;wBAAK;qBAAE;oBACnF,gBAAgB;wBAAE,UAAU;wBAAG,WAAW;wBAAG,MAAM;4BAAE,OAAO;4BAAK,MAAM;wBAAM;oBAAE;oBAC/E,UAAU;gBACZ;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,kBAAkB;oBAClB,OAAO;oBACP,UAAU;oBACV,QAAQ;wBAAC;4BAAE,KAAK;4BAA4B,KAAK;4BAAkB,WAAW;wBAAK;qBAAE;oBACrF,UAAU;gBACZ;aACD;YAED;8CAAW;oBACT,YAAY;oBACZ,WAAW;gBACb;6CAAG;QACL;qCAAG,EAAE;IAEL,MAAM,kBAAkB,CAAC;QACvB,QAAQ;YACN,IAAI,QAAQ,EAAE;YACd,OAAO,QAAQ,KAAK;YACpB,OAAO,QAAQ,KAAK;YACpB,WAAW,QAAQ,SAAS;YAC5B,OAAO,QAAQ,MAAM,CAAC,EAAE,EAAE;QAC5B;QACA,sIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;IAChB;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAQ,WAAU;sBACjB,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAG,WAAU;sCAAmC;;;;;;;;;;;kCAEnD,6LAAC;wBAAI,WAAU;kCACZ;+BAAI,MAAM;yBAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6LAAC;gCAAY,WAAU;;kDACrB,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAU;;;;;;;+BAHP;;;;;;;;;;;;;;;;;;;;;IAUtB;IAEA,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,6LAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAKzD,6LAAC;oBAAI,WAAU;8BACZ,SAAS,GAAG,CAAC,CAAC,wBACb,6LAAC;4BAAqB,WAAU;;8CAE9B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,KAAK,QAAQ,MAAM,CAAC,EAAE,EAAE;4CACxB,KAAK,QAAQ,MAAM,CAAC,EAAE,EAAE;4CACxB,WAAU;;;;;;wCAEX,QAAQ,UAAU,kBACjB,6LAAC;4CAAK,WAAU;sDAAwE;;;;;;wCAIzF,QAAQ,SAAS,kBAChB,6LAAC;4CAAK,WAAU;sDAA2E;;;;;;sDAM7F,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAM,CAAC,UAAU,EAAE,QAAQ,EAAE,EAAE;oDAC/B,WAAU;8DAEV,cAAA,6LAAC,gNAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;;;;;;8DAErB,6LAAC;oDACC,SAAS,IAAM,gBAAgB;oDAC/B,WAAU;8DAEV,cAAA,6LAAC,kOAAA,CAAA,mBAAgB;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;8CAMlC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDACX,QAAQ,KAAK;;;;;;sDAEhB,6LAAC;4CAAE,WAAU;sDACV,QAAQ,gBAAgB;;;;;;wCAI1B,QAAQ,QAAQ,KAAK,iBAAiB,QAAQ,cAAc,kBAC3D,6LAAC;4CAAI,WAAU;;gDACZ,QAAQ,cAAc,CAAC,QAAQ,kBAC9B,6LAAC;;wDAAM,QAAQ,cAAc,CAAC,QAAQ;wDAAC;;;;;;;gDAExC,QAAQ,cAAc,CAAC,SAAS,kBAC/B,6LAAC;;wDAAM,QAAQ,cAAc,CAAC,SAAS;wDAAC;;;;;;;gDAEzC,QAAQ,cAAc,CAAC,IAAI,kBAC1B,6LAAC;;wDAAM,QAAQ,cAAc,CAAC,IAAI,CAAC,KAAK;wDAAC;wDAAE,QAAQ,cAAc,CAAC,IAAI,CAAC,IAAI;;;;;;;;;;;;;sDAMjF,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACZ,QAAQ,SAAS,iBAChB;;0EACE,6LAAC;gEAAK,WAAU;;oEAAkC;oEAC9C,QAAQ,SAAS;;;;;;;0EAErB,6LAAC;gEAAK,WAAU;;oEAAqC;oEACjD,QAAQ,KAAK;;;;;;;;qFAInB,6LAAC;wDAAK,WAAU;;4DAAkC;4DAC9C,QAAQ,KAAK;;;;;;;;;;;;8DAIrB,6LAAC;oDACC,SAAS,IAAM,gBAAgB;oDAC/B,WAAU;8DAEV,cAAA,6LAAC,kOAAA,CAAA,mBAAgB;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;2BAlF1B,QAAQ,EAAE;;;;;;;;;;8BA0FxB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;wBACH,MAAK;wBACL,WAAU;kCACX;;;;;;;;;;;;;;;;;;;;;;AAOX;GA1MwB;;QAGF,kJAAA,CAAA,UAAO;;;KAHL", "debugId": null}}, {"offset": {"line": 1093, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/coding/luyari/luyaridesignstemplate/src/components/home/<USER>"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport { \n  HomeIcon, \n  PaintBrushIcon, \n  DevicePhoneMobileIcon,\n  PhotoIcon,\n  DocumentTextIcon,\n  CubeIcon \n} from '@heroicons/react/24/outline';\n\nexport default function Categories() {\n  const categories = [\n    {\n      name: 'House Plans',\n      description: 'Modern and traditional house designs',\n      href: '/collections/house-plans',\n      icon: HomeIcon,\n      color: 'bg-blue-500',\n      count: '2,500+',\n    },\n    {\n      name: 'UI Kits',\n      description: 'Complete interface design systems',\n      href: '/collections/ui-kits',\n      icon: DevicePhoneMobileIcon,\n      color: 'bg-purple-500',\n      count: '800+',\n    },\n    {\n      name: 'Templates',\n      description: 'Ready-to-use design templates',\n      href: '/collections/templates',\n      icon: DocumentTextIcon,\n      color: 'bg-green-500',\n      count: '1,200+',\n    },\n    {\n      name: 'Graphics',\n      description: 'Icons, illustrations, and graphics',\n      href: '/collections/graphics',\n      icon: PaintBrushIcon,\n      color: 'bg-orange-500',\n      count: '3,000+',\n    },\n    {\n      name: 'Mockups',\n      description: 'Professional presentation mockups',\n      href: '/collections/mockups',\n      icon: PhotoIcon,\n      color: 'bg-pink-500',\n      count: '600+',\n    },\n    {\n      name: '3D Models',\n      description: 'High-quality 3D assets',\n      href: '/collections/3d-models',\n      icon: CubeIcon,\n      color: 'bg-indigo-500',\n      count: '400+',\n    },\n  ];\n\n  return (\n    <section className=\"py-16 bg-gray-50\">\n      <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n        <div className=\"text-center mb-12\">\n          <h2 className=\"text-3xl font-bold text-gray-900 mb-4\">Browse by Category</h2>\n          <p className=\"text-lg text-gray-600 max-w-2xl mx-auto\">\n            Find exactly what you need from our carefully curated categories\n          </p>\n        </div>\n\n        <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6\">\n          {categories.map((category) => {\n            const IconComponent = category.icon;\n            return (\n              <Link\n                key={category.name}\n                href={category.href}\n                className=\"group relative bg-white rounded-xl p-6 shadow-sm border border-gray-200 hover:shadow-lg hover:border-gray-300 transition-all duration-300\"\n              >\n                <div className=\"flex items-start space-x-4\">\n                  <div className={`${category.color} p-3 rounded-lg group-hover:scale-110 transition-transform`}>\n                    <IconComponent className=\"h-6 w-6 text-white\" />\n                  </div>\n                  <div className=\"flex-1\">\n                    <h3 className=\"text-lg font-semibold text-gray-900 group-hover:text-blue-600 transition-colors\">\n                      {category.name}\n                    </h3>\n                    <p className=\"text-sm text-gray-600 mt-1\">\n                      {category.description}\n                    </p>\n                    <div className=\"mt-3 flex items-center justify-between\">\n                      <span className=\"text-sm font-medium text-gray-500\">\n                        {category.count} items\n                      </span>\n                      <span className=\"text-blue-600 text-sm font-medium group-hover:translate-x-1 transition-transform\">\n                        Browse →\n                      </span>\n                    </div>\n                  </div>\n                </div>\n              </Link>\n            );\n          })}\n        </div>\n\n        {/* Featured Category Banner */}\n        <div className=\"mt-16 bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl p-8 text-center text-white\">\n          <h3 className=\"text-2xl font-bold mb-4\">House Plans Collection</h3>\n          <p className=\"text-lg opacity-90 mb-6 max-w-2xl mx-auto\">\n            Discover our extensive collection of modern house plans, from cozy bungalows to luxury mansions. \n            All plans include detailed blueprints and construction documents.\n          </p>\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n            <Link\n              href=\"/collections/house-plans\"\n              className=\"inline-flex items-center justify-center px-6 py-3 bg-white text-blue-600 font-medium rounded-lg hover:bg-gray-100 transition-colors\"\n            >\n              Browse House Plans\n            </Link>\n            <Link\n              href=\"/custom-plan\"\n              className=\"inline-flex items-center justify-center px-6 py-3 border border-white text-white font-medium rounded-lg hover:bg-white hover:text-blue-600 transition-colors\"\n            >\n              Custom Design\n            </Link>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AAYe,SAAS;IACtB,MAAM,aAAa;QACjB;YACE,MAAM;YACN,aAAa;YACb,MAAM;YACN,MAAM,kNAAA,CAAA,WAAQ;YACd,OAAO;YACP,OAAO;QACT;QACA;YACE,MAAM;YACN,aAAa;YACb,MAAM;YACN,MAAM,4OAAA,CAAA,wBAAqB;YAC3B,OAAO;YACP,OAAO;QACT;QACA;YACE,MAAM;YACN,aAAa;YACb,MAAM;YACN,MAAM,kOAAA,CAAA,mBAAgB;YACtB,OAAO;YACP,OAAO;QACT;QACA;YACE,MAAM;YACN,aAAa;YACb,MAAM;YACN,MAAM,8NAAA,CAAA,iBAAc;YACpB,OAAO;YACP,OAAO;QACT;QACA;YACE,MAAM;YAC<PERSON>,aAAa;YACb,MAAM;YACN,MAAM,oNAAA,CAAA,YAAS;YACf,OAAO;YACP,OAAO;QACT;QACA;YACE,MAAM;YACN,aAAa;YACb,MAAM;YACN,MAAM,kNAAA,CAAA,WAAQ;YACd,OAAO;YACP,OAAO;QACT;KACD;IAED,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,6LAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAKzD,6LAAC;oBAAI,WAAU;8BACZ,WAAW,GAAG,CAAC,CAAC;wBACf,MAAM,gBAAgB,SAAS,IAAI;wBACnC,qBACE,6LAAC,+JAAA,CAAA,UAAI;4BAEH,MAAM,SAAS,IAAI;4BACnB,WAAU;sCAEV,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAW,GAAG,SAAS,KAAK,CAAC,0DAA0D,CAAC;kDAC3F,cAAA,6LAAC;4CAAc,WAAU;;;;;;;;;;;kDAE3B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DACX,SAAS,IAAI;;;;;;0DAEhB,6LAAC;gDAAE,WAAU;0DACV,SAAS,WAAW;;;;;;0DAEvB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;;4DACb,SAAS,KAAK;4DAAC;;;;;;;kEAElB,6LAAC;wDAAK,WAAU;kEAAmF;;;;;;;;;;;;;;;;;;;;;;;;2BAnBpG,SAAS,IAAI;;;;;oBA2BxB;;;;;;8BAIF,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA0B;;;;;;sCACxC,6LAAC;4BAAE,WAAU;sCAA4C;;;;;;sCAIzD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb;KA1HwB", "debugId": null}}, {"offset": {"line": 1359, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/coding/luyari/luyaridesignstemplate/src/components/home/<USER>"], "sourcesContent": ["'use client';\n\nimport { StarIcon } from '@heroicons/react/24/solid';\n\nexport default function Testimonials() {\n  const testimonials = [\n    {\n      id: 1,\n      name: '<PERSON>',\n      role: 'Architect',\n      company: 'Johnson Design Studio',\n      content: 'The house plans from Luyari Designs are incredibly detailed and professional. They saved us weeks of work and our clients love the modern designs.',\n      rating: 5,\n      avatar: '/api/placeholder/64/64',\n    },\n    {\n      id: 2,\n      name: '<PERSON>',\n      role: 'UI/UX Designer',\n      company: 'TechStart Inc.',\n      content: 'Amazing UI kits with pixel-perfect designs. The components are well-organized and easy to customize. Highly recommend for any design project.',\n      rating: 5,\n      avatar: '/api/placeholder/64/64',\n    },\n    {\n      id: 3,\n      name: '<PERSON>',\n      role: 'Freelance Designer',\n      company: 'Independent',\n      content: 'I\\'ve purchased multiple templates and they\\'re all top quality. Great value for money and excellent customer support. Will definitely buy again.',\n      rating: 5,\n      avatar: '/api/placeholder/64/64',\n    },\n    {\n      id: 4,\n      name: '<PERSON>',\n      role: 'Construction Manager',\n      company: 'BuildRight Construction',\n      content: 'The construction documents are thorough and accurate. We\\'ve built several houses using these plans without any issues. Professional quality.',\n      rating: 5,\n      avatar: '/api/placeholder/64/64',\n    },\n    {\n      id: 5,\n      name: '<PERSON>',\n      role: 'Brand Designer',\n      company: 'Creative Agency',\n      content: 'Fantastic collection of brand templates. They\\'re modern, versatile, and save so much time. The quality is consistently excellent.',\n      rating: 5,\n      avatar: '/api/placeholder/64/64',\n    },\n    {\n      id: 6,\n      name: 'James Miller',\n      role: 'Real Estate Developer',\n      company: 'Miller Properties',\n      content: 'We use these house plans for our residential developments. The designs are market-tested and our buyers love them. Great investment.',\n      rating: 5,\n      avatar: '/api/placeholder/64/64',\n    },\n  ];\n\n  return (\n    <section className=\"py-16 bg-white\">\n      <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n        <div className=\"text-center mb-12\">\n          <h2 className=\"text-3xl font-bold text-gray-900 mb-4\">What Our Customers Say</h2>\n          <p className=\"text-lg text-gray-600 max-w-2xl mx-auto\">\n            Join thousands of satisfied customers who trust Luyari Designs for their creative projects\n          </p>\n        </div>\n\n        {/* Stats */}\n        <div className=\"grid grid-cols-1 sm:grid-cols-4 gap-8 mb-16 text-center\">\n          <div>\n            <div className=\"text-3xl font-bold text-blue-600\">50,000+</div>\n            <div className=\"text-sm text-gray-600 mt-1\">Happy Customers</div>\n          </div>\n          <div>\n            <div className=\"text-3xl font-bold text-blue-600\">5,000+</div>\n            <div className=\"text-sm text-gray-600 mt-1\">Design Templates</div>\n          </div>\n          <div>\n            <div className=\"text-3xl font-bold text-blue-600\">4.9/5</div>\n            <div className=\"text-sm text-gray-600 mt-1\">Average Rating</div>\n          </div>\n          <div>\n            <div className=\"text-3xl font-bold text-blue-600\">99%</div>\n            <div className=\"text-sm text-gray-600 mt-1\">Satisfaction Rate</div>\n          </div>\n        </div>\n\n        {/* Testimonials Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n          {testimonials.map((testimonial) => (\n            <div\n              key={testimonial.id}\n              className=\"bg-gray-50 rounded-xl p-6 border border-gray-200 hover:shadow-lg transition-shadow\"\n            >\n              {/* Rating */}\n              <div className=\"flex items-center mb-4\">\n                {[...Array(testimonial.rating)].map((_, i) => (\n                  <StarIcon key={i} className=\"h-5 w-5 text-yellow-400\" />\n                ))}\n              </div>\n\n              {/* Content */}\n              <p className=\"text-gray-700 mb-6 leading-relaxed\">\n                \"{testimonial.content}\"\n              </p>\n\n              {/* Author */}\n              <div className=\"flex items-center\">\n                <img\n                  src={testimonial.avatar}\n                  alt={testimonial.name}\n                  className=\"w-12 h-12 rounded-full object-cover\"\n                />\n                <div className=\"ml-3\">\n                  <div className=\"font-semibold text-gray-900\">{testimonial.name}</div>\n                  <div className=\"text-sm text-gray-600\">\n                    {testimonial.role}\n                    {testimonial.company !== 'Independent' && (\n                      <span> at {testimonial.company}</span>\n                    )}\n                  </div>\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n\n        {/* CTA */}\n        <div className=\"text-center mt-12\">\n          <p className=\"text-lg text-gray-600 mb-6\">\n            Ready to join our community of satisfied customers?\n          </p>\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n            <a\n              href=\"/shop\"\n              className=\"inline-flex items-center justify-center px-8 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors\"\n            >\n              Start Shopping\n            </a>\n            <a\n              href=\"/about\"\n              className=\"inline-flex items-center justify-center px-8 py-3 border border-gray-300 text-gray-700 font-medium rounded-lg hover:bg-gray-50 transition-colors\"\n            >\n              Learn More\n            </a>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIe,SAAS;IACtB,MAAM,eAAe;QACnB;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,SAAS;YACT,SAAS;YACT,QAAQ;YACR,QAAQ;QACV;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,SAAS;YACT,SAAS;YACT,QAAQ;YACR,QAAQ;QACV;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,SAAS;YACT,SAAS;YACT,QAAQ;YACR,QAAQ;QACV;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,SAAS;YACT,SAAS;YACT,QAAQ;YACR,QAAQ;QACV;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,SAAS;YACT,SAAS;YACT,QAAQ;YACR,QAAQ;QACV;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,SAAS;YACT,SAAS;YACT,QAAQ;YACR,QAAQ;QACV;KACD;IAED,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,6LAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAMzD,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC;oCAAI,WAAU;8CAAmC;;;;;;8CAClD,6LAAC;oCAAI,WAAU;8CAA6B;;;;;;;;;;;;sCAE9C,6LAAC;;8CACC,6LAAC;oCAAI,WAAU;8CAAmC;;;;;;8CAClD,6LAAC;oCAAI,WAAU;8CAA6B;;;;;;;;;;;;sCAE9C,6LAAC;;8CACC,6LAAC;oCAAI,WAAU;8CAAmC;;;;;;8CAClD,6LAAC;oCAAI,WAAU;8CAA6B;;;;;;;;;;;;sCAE9C,6LAAC;;8CACC,6LAAC;oCAAI,WAAU;8CAAmC;;;;;;8CAClD,6LAAC;oCAAI,WAAU;8CAA6B;;;;;;;;;;;;;;;;;;8BAKhD,6LAAC;oBAAI,WAAU;8BACZ,aAAa,GAAG,CAAC,CAAC,4BACjB,6LAAC;4BAEC,WAAU;;8CAGV,6LAAC;oCAAI,WAAU;8CACZ;2CAAI,MAAM,YAAY,MAAM;qCAAE,CAAC,GAAG,CAAC,CAAC,GAAG,kBACtC,6LAAC,gNAAA,CAAA,WAAQ;4CAAS,WAAU;2CAAb;;;;;;;;;;8CAKnB,6LAAC;oCAAE,WAAU;;wCAAqC;wCAC9C,YAAY,OAAO;wCAAC;;;;;;;8CAIxB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,KAAK,YAAY,MAAM;4CACvB,KAAK,YAAY,IAAI;4CACrB,WAAU;;;;;;sDAEZ,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAA+B,YAAY,IAAI;;;;;;8DAC9D,6LAAC;oDAAI,WAAU;;wDACZ,YAAY,IAAI;wDAChB,YAAY,OAAO,KAAK,+BACvB,6LAAC;;gEAAK;gEAAK,YAAY,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;2BA3BjC,YAAY,EAAE;;;;;;;;;;8BAqCzB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAE,WAAU;sCAA6B;;;;;;sCAG1C,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,6LAAC;oCACC,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb;KAvJwB", "debugId": null}}, {"offset": {"line": 1726, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/coding/luyari/luyaridesignstemplate/src/components/layout/Footer.jsx"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport {\n  EnvelopeIcon,\n  PhoneIcon,\n  MapPinIcon\n} from '@heroicons/react/24/outline';\n\n// Custom social media icons as SVG components\nconst FacebookIcon = ({ className }) => (\n  <svg className={className} fill=\"currentColor\" viewBox=\"0 0 24 24\">\n    <path d=\"M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z\"/>\n  </svg>\n);\n\nconst TwitterIcon = ({ className }) => (\n  <svg className={className} fill=\"currentColor\" viewBox=\"0 0 24 24\">\n    <path d=\"M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z\"/>\n  </svg>\n);\n\nconst InstagramIcon = ({ className }) => (\n  <svg className={className} fill=\"currentColor\" viewBox=\"0 0 24 24\">\n    <path d=\"M12.017 0C8.396 0 8.025.044 6.979.207 5.934.37 5.226.648 4.61 1.014c-.616.366-1.138.854-1.504 1.47C2.74 3.1 2.462 3.808 2.299 4.854 2.136 5.9 2.092 6.271 2.092 9.892s.044 3.992.207 5.038c.163 1.046.441 1.754.807 2.37.366.616.854 1.138 1.47 1.504.616.366 1.324.644 2.37.807 1.046.163 1.417.207 5.038.207s3.992-.044 5.038-.207c1.046-.163 1.754-.441 2.37-.807.616-.366 1.138-.854 1.504-1.47.366-.616.644-1.324.807-2.37.163-1.046.207-1.417.207-5.038s-.044-3.992-.207-5.038c-.163-1.046-.441-1.754-.807-2.37-.366-.616-.854-1.138-1.47-1.504C17.754 2.74 17.046 2.462 16 2.299 14.954 2.136 14.583 2.092 10.962 2.092zm0 1.838c3.581 0 4.002.015 5.415.207.925.042 1.504.195 1.856.324.466.181.8.398 1.15.748.35.35.567.684.748 1.15.129.352.282.931.324 1.856.192 1.413.207 1.834.207 5.415s-.015 4.002-.207 5.415c-.042.925-.195 1.504-.324 1.856-.181.466-.398.8-.748 1.15-.35.35-.684.567-1.15.748-.352.129-.931.282-1.856.324-1.413.192-1.834.207-5.415.207s-4.002-.015-5.415-.207c-.925-.042-1.504-.195-1.856-.324-.466-.181-.8-.398-1.15-.748-.35-.35-.567-.684-.748-1.15-.129-.352-.282-.931-.324-1.856C3.93 14.004 3.915 13.583 3.915 10.002s.015-4.002.207-5.415c.042-.925.195-1.504.324-1.856.181-.466.398-.8.748-1.15.35-.35.684-.567 1.15-.748.352-.129.931-.282 1.856-.324C8.613 1.847 9.034 1.832 12.615 1.832l-.598 0z\"/>\n  </svg>\n);\n\nconst LinkedInIcon = ({ className }) => (\n  <svg className={className} fill=\"currentColor\" viewBox=\"0 0 24 24\">\n    <path d=\"M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z\"/>\n  </svg>\n);\n\nexport default function Footer() {\n  const navigation = {\n    shop: [\n      { name: 'All Products', href: '/shop' },\n      { name: 'House Plans', href: '/collections/house-plans' },\n      { name: 'UI Kits', href: '/collections/ui-kits' },\n      { name: 'Templates', href: '/collections/templates' },\n      { name: 'Graphics', href: '/collections/graphics' },\n      { name: 'Mockups', href: '/collections/mockups' },\n    ],\n    company: [\n      { name: 'About Us', href: '/about' },\n      { name: 'Contact', href: '/contact' },\n      { name: 'Blog', href: '/blog' },\n      { name: 'Careers', href: '/careers' },\n      { name: 'Press', href: '/press' },\n    ],\n    support: [\n      { name: 'Help Center', href: '/help' },\n      { name: 'FAQ', href: '/faq' },\n      { name: 'License', href: '/license' },\n      { name: 'Refund Policy', href: '/refund-policy' },\n      { name: 'Terms of Service', href: '/terms' },\n      { name: 'Privacy Policy', href: '/privacy' },\n    ],\n    account: [\n      { name: 'Sign In', href: '/auth/signin' },\n      { name: 'Create Account', href: '/auth/signup' },\n      { name: 'My Account', href: '/account' },\n      { name: 'Order History', href: '/account/orders' },\n      { name: 'Downloads', href: '/account/downloads' },\n    ],\n  };\n\n  const socialLinks = [\n    { name: 'Facebook', href: '#', icon: FacebookIcon },\n    { name: 'Twitter', href: '#', icon: TwitterIcon },\n    { name: 'Instagram', href: '#', icon: InstagramIcon },\n    { name: 'LinkedIn', href: '#', icon: LinkedInIcon },\n  ];\n\n  return (\n    <footer className=\"bg-gray-900 text-white\">\n      <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n        {/* Main Footer Content */}\n        <div className=\"py-16\">\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-8\">\n            {/* Company Info */}\n            <div className=\"lg:col-span-2\">\n              <div className=\"flex items-center space-x-2 mb-4\">\n                <div className=\"w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center\">\n                  <span className=\"text-white font-bold text-sm\">LD</span>\n                </div>\n                <span className=\"text-xl font-bold\">Luyari Designs</span>\n              </div>\n              <p className=\"text-gray-400 mb-6 max-w-md\">\n                Premium design templates and digital assets for creators, architects, and designers.\n                Bringing your creative vision to life with high-quality, professional designs.\n              </p>\n\n              {/* Contact Info */}\n              <div className=\"space-y-3\">\n                <div className=\"flex items-center space-x-3\">\n                  <EnvelopeIcon className=\"h-5 w-5 text-gray-400\" />\n                  <span className=\"text-gray-400\"><EMAIL></span>\n                </div>\n                <div className=\"flex items-center space-x-3\">\n                  <PhoneIcon className=\"h-5 w-5 text-gray-400\" />\n                  <span className=\"text-gray-400\">+****************</span>\n                </div>\n                <div className=\"flex items-center space-x-3\">\n                  <MapPinIcon className=\"h-5 w-5 text-gray-400\" />\n                  <span className=\"text-gray-400\">San Francisco, CA</span>\n                </div>\n              </div>\n            </div>\n\n            {/* Navigation Links */}\n            <div>\n              <h3 className=\"text-sm font-semibold uppercase tracking-wider mb-4\">Shop</h3>\n              <ul className=\"space-y-3\">\n                {navigation.shop.map((item) => (\n                  <li key={item.name}>\n                    <Link\n                      href={item.href}\n                      className=\"text-gray-400 hover:text-white transition-colors\"\n                    >\n                      {item.name}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </div>\n\n            <div>\n              <h3 className=\"text-sm font-semibold uppercase tracking-wider mb-4\">Company</h3>\n              <ul className=\"space-y-3\">\n                {navigation.company.map((item) => (\n                  <li key={item.name}>\n                    <Link\n                      href={item.href}\n                      className=\"text-gray-400 hover:text-white transition-colors\"\n                    >\n                      {item.name}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </div>\n\n            <div>\n              <h3 className=\"text-sm font-semibold uppercase tracking-wider mb-4\">Support</h3>\n              <ul className=\"space-y-3\">\n                {navigation.support.map((item) => (\n                  <li key={item.name}>\n                    <Link\n                      href={item.href}\n                      className=\"text-gray-400 hover:text-white transition-colors\"\n                    >\n                      {item.name}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </div>\n\n            <div>\n              <h3 className=\"text-sm font-semibold uppercase tracking-wider mb-4\">Account</h3>\n              <ul className=\"space-y-3\">\n                {navigation.account.map((item) => (\n                  <li key={item.name}>\n                    <Link\n                      href={item.href}\n                      className=\"text-gray-400 hover:text-white transition-colors\"\n                    >\n                      {item.name}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </div>\n          </div>\n        </div>\n\n        {/* Newsletter Signup */}\n        <div className=\"border-t border-gray-800 py-8\">\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8 items-center\">\n            <div>\n              <h3 className=\"text-lg font-semibold mb-2\">Stay Updated</h3>\n              <p className=\"text-gray-400\">\n                Get the latest design templates and exclusive offers delivered to your inbox.\n              </p>\n            </div>\n            <div className=\"flex flex-col sm:flex-row gap-3\">\n              <input\n                type=\"email\"\n                placeholder=\"Enter your email\"\n                className=\"flex-1 px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              />\n              <button className=\"px-6 py-2 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors\">\n                Subscribe\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* Bottom Footer */}\n        <div className=\"border-t border-gray-800 py-6\">\n          <div className=\"flex flex-col md:flex-row justify-between items-center\">\n            <div className=\"text-gray-400 text-sm\">\n              © 2024 Luyari Designs. All rights reserved.\n            </div>\n\n            {/* Social Links */}\n            <div className=\"flex space-x-4 mt-4 md:mt-0\">\n              {socialLinks.map((item) => {\n                const IconComponent = item.icon;\n                return (\n                  <a\n                    key={item.name}\n                    href={item.href}\n                    className=\"text-gray-400 hover:text-white transition-colors\"\n                  >\n                    <span className=\"sr-only\">{item.name}</span>\n                    <IconComponent className=\"h-5 w-5\" />\n                  </a>\n                );\n              })}\n            </div>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAHA;;;;AASA,8CAA8C;AAC9C,MAAM,eAAe,CAAC,EAAE,SAAS,EAAE,iBACjC,6LAAC;QAAI,WAAW;QAAW,MAAK;QAAe,SAAQ;kBACrD,cAAA,6LAAC;YAAK,GAAE;;;;;;;;;;;KAFN;AAMN,MAAM,cAAc,CAAC,EAAE,SAAS,EAAE,iBAChC,6LAAC;QAAI,WAAW;QAAW,MAAK;QAAe,SAAQ;kBACrD,cAAA,6LAAC;YAAK,GAAE;;;;;;;;;;;MAFN;AAMN,MAAM,gBAAgB,CAAC,EAAE,SAAS,EAAE,iBAClC,6LAAC;QAAI,WAAW;QAAW,MAAK;QAAe,SAAQ;kBACrD,cAAA,6LAAC;YAAK,GAAE;;;;;;;;;;;MAFN;AAMN,MAAM,eAAe,CAAC,EAAE,SAAS,EAAE,iBACjC,6LAAC;QAAI,WAAW;QAAW,MAAK;QAAe,SAAQ;kBACrD,cAAA,6LAAC;YAAK,GAAE;;;;;;;;;;;MAFN;AAMS,SAAS;IACtB,MAAM,aAAa;QACjB,MAAM;YACJ;gBAAE,MAAM;gBAAgB,MAAM;YAAQ;YACtC;gBAAE,MAAM;gBAAe,MAAM;YAA2B;YACxD;gBAAE,MAAM;gBAAW,MAAM;YAAuB;YAChD;gBAAE,MAAM;gBAAa,MAAM;YAAyB;YACpD;gBAAE,MAAM;gBAAY,MAAM;YAAwB;YAClD;gBAAE,MAAM;gBAAW,MAAM;YAAuB;SACjD;QACD,SAAS;YACP;gBAAE,MAAM;gBAAY,MAAM;YAAS;YACnC;gBAAE,MAAM;gBAAW,MAAM;YAAW;YACpC;gBAAE,MAAM;gBAAQ,MAAM;YAAQ;YAC9B;gBAAE,MAAM;gBAAW,MAAM;YAAW;YACpC;gBAAE,MAAM;gBAAS,MAAM;YAAS;SACjC;QACD,SAAS;YACP;gBAAE,MAAM;gBAAe,MAAM;YAAQ;YACrC;gBAAE,MAAM;gBAAO,MAAM;YAAO;YAC5B;gBAAE,MAAM;gBAAW,MAAM;YAAW;YACpC;gBAAE,MAAM;gBAAiB,MAAM;YAAiB;YAChD;gBAAE,MAAM;gBAAoB,MAAM;YAAS;YAC3C;gBAAE,MAAM;gBAAkB,MAAM;YAAW;SAC5C;QACD,SAAS;YACP;gBAAE,MAAM;gBAAW,MAAM;YAAe;YACxC;gBAAE,MAAM;gBAAkB,MAAM;YAAe;YAC/C;gBAAE,MAAM;gBAAc,MAAM;YAAW;YACvC;gBAAE,MAAM;gBAAiB,MAAM;YAAkB;YACjD;gBAAE,MAAM;gBAAa,MAAM;YAAqB;SACjD;IACH;IAEA,MAAM,cAAc;QAClB;YAAE,MAAM;YAAY,MAAM;YAAK,MAAM;QAAa;QAClD;YAAE,MAAM;YAAW,MAAM;YAAK,MAAM;QAAY;QAChD;YAAE,MAAM;YAAa,MAAM;YAAK,MAAM;QAAc;QACpD;YAAE,MAAM;YAAY,MAAM;YAAK,MAAM;QAAa;KACnD;IAED,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAK,WAAU;8DAA+B;;;;;;;;;;;0DAEjD,6LAAC;gDAAK,WAAU;0DAAoB;;;;;;;;;;;;kDAEtC,6LAAC;wCAAE,WAAU;kDAA8B;;;;;;kDAM3C,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,0NAAA,CAAA,eAAY;wDAAC,WAAU;;;;;;kEACxB,6LAAC;wDAAK,WAAU;kEAAgB;;;;;;;;;;;;0DAElC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,oNAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;kEACrB,6LAAC;wDAAK,WAAU;kEAAgB;;;;;;;;;;;;0DAElC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,sNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;kEACtB,6LAAC;wDAAK,WAAU;kEAAgB;;;;;;;;;;;;;;;;;;;;;;;;0CAMtC,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAsD;;;;;;kDACpE,6LAAC;wCAAG,WAAU;kDACX,WAAW,IAAI,CAAC,GAAG,CAAC,CAAC,qBACpB,6LAAC;0DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAM,KAAK,IAAI;oDACf,WAAU;8DAET,KAAK,IAAI;;;;;;+CALL,KAAK,IAAI;;;;;;;;;;;;;;;;0CAYxB,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAsD;;;;;;kDACpE,6LAAC;wCAAG,WAAU;kDACX,WAAW,OAAO,CAAC,GAAG,CAAC,CAAC,qBACvB,6LAAC;0DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAM,KAAK,IAAI;oDACf,WAAU;8DAET,KAAK,IAAI;;;;;;+CALL,KAAK,IAAI;;;;;;;;;;;;;;;;0CAYxB,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAsD;;;;;;kDACpE,6LAAC;wCAAG,WAAU;kDACX,WAAW,OAAO,CAAC,GAAG,CAAC,CAAC,qBACvB,6LAAC;0DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAM,KAAK,IAAI;oDACf,WAAU;8DAET,KAAK,IAAI;;;;;;+CALL,KAAK,IAAI;;;;;;;;;;;;;;;;0CAYxB,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAsD;;;;;;kDACpE,6LAAC;wCAAG,WAAU;kDACX,WAAW,OAAO,CAAC,GAAG,CAAC,CAAC,qBACvB,6LAAC;0DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAM,KAAK,IAAI;oDACf,WAAU;8DAET,KAAK,IAAI;;;;;;+CALL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAe5B,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,6LAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;0CAI/B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,WAAU;;;;;;kDAEZ,6LAAC;wCAAO,WAAU;kDAA8F;;;;;;;;;;;;;;;;;;;;;;;8BAQtH,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CAAwB;;;;;;0CAKvC,6LAAC;gCAAI,WAAU;0CACZ,YAAY,GAAG,CAAC,CAAC;oCAChB,MAAM,gBAAgB,KAAK,IAAI;oCAC/B,qBACE,6LAAC;wCAEC,MAAM,KAAK,IAAI;wCACf,WAAU;;0DAEV,6LAAC;gDAAK,WAAU;0DAAW,KAAK,IAAI;;;;;;0DACpC,6LAAC;gDAAc,WAAU;;;;;;;uCALpB,KAAK,IAAI;;;;;gCAQpB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOd;MAlMwB", "debugId": null}}]}