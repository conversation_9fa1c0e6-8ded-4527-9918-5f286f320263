"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("react"),t=require("@react-three/fiber"),r=require("three"),n=require("../helpers/constants.cjs.js");function a(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var n=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,n.get?n:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,Object.freeze(t)}var o=a(e);class i extends r.ShaderMaterial{constructor(){super({uniforms:{time:{value:0},fade:{value:1}},vertexShader:"\n      uniform float time;\n      attribute float size;\n      varying vec3 vColor;\n      void main() {\n        vColor = color;\n        vec4 mvPosition = modelViewMatrix * vec4(position, 0.5);\n        gl_PointSize = size * (30.0 / -mvPosition.z) * (3.0 + sin(time + 100.0));\n        gl_Position = projectionMatrix * mvPosition;\n      }",fragmentShader:`\n      uniform sampler2D pointTexture;\n      uniform float fade;\n      varying vec3 vColor;\n      void main() {\n        float opacity = 1.0;\n        if (fade == 1.0) {\n          float d = distance(gl_PointCoord, vec2(0.5, 0.5));\n          opacity = 1.0 / (1.0 + exp(16.0 * (d - 0.25)));\n        }\n        gl_FragColor = vec4(vColor, opacity);\n\n        #include <tonemapping_fragment>\n\t      #include <${n.version>=154?"colorspace_fragment":"encodings_fragment"}>\n      }`})}}const c=e=>(new r.Vector3).setFromSpherical(new r.Spherical(e,Math.acos(1-2*Math.random()),2*Math.random()*Math.PI)),s=o.forwardRef((({radius:e=100,depth:n=50,count:a=5e3,saturation:s=0,factor:l=4,fade:u=!1,speed:f=1},m)=>{const d=o.useRef(null),[p,v,h]=o.useMemo((()=>{const t=[],o=[],i=Array.from({length:a},(()=>(.5+.5*Math.random())*l)),u=new r.Color;let f=e+n;const m=n/a;for(let e=0;e<a;e++)f-=m*Math.random(),t.push(...c(f).toArray()),u.setHSL(e/a,s,.9),o.push(u.r,u.g,u.b);return[new Float32Array(t),new Float32Array(o),new Float32Array(i)]}),[a,n,l,e,s]);t.useFrame((e=>d.current&&(d.current.uniforms.time.value=e.clock.elapsedTime*f)));const[g]=o.useState((()=>new i));return o.createElement("points",{ref:m},o.createElement("bufferGeometry",null,o.createElement("bufferAttribute",{attach:"attributes-position",args:[p,3]}),o.createElement("bufferAttribute",{attach:"attributes-color",args:[v,3]}),o.createElement("bufferAttribute",{attach:"attributes-size",args:[h,1]})),o.createElement("primitive",{ref:d,object:g,attach:"material",blending:r.AdditiveBlending,"uniforms-fade-value":u,depthWrite:!1,transparent:!0,vertexColors:!0}))}));exports.Stars=s;
