'use client';

import { StarIcon } from '@heroicons/react/24/solid';

export default function Testimonials() {
  const testimonials = [
    {
      id: 1,
      name: '<PERSON>',
      role: 'Architect',
      company: 'Johnson Design Studio',
      content: 'The house plans from Luyari Designs are incredibly detailed and professional. They saved us weeks of work and our clients love the modern designs.',
      rating: 5,
      avatar: '/api/placeholder/64/64',
    },
    {
      id: 2,
      name: '<PERSON>',
      role: 'UI/UX Designer',
      company: 'TechStart Inc.',
      content: 'Amazing UI kits with pixel-perfect designs. The components are well-organized and easy to customize. Highly recommend for any design project.',
      rating: 5,
      avatar: '/api/placeholder/64/64',
    },
    {
      id: 3,
      name: '<PERSON>',
      role: 'Freelance Designer',
      company: 'Independent',
      content: 'I\'ve purchased multiple templates and they\'re all top quality. Great value for money and excellent customer support. Will definitely buy again.',
      rating: 5,
      avatar: '/api/placeholder/64/64',
    },
    {
      id: 4,
      name: '<PERSON>',
      role: 'Construction Manager',
      company: 'BuildRight Construction',
      content: 'The construction documents are thorough and accurate. We\'ve built several houses using these plans without any issues. Professional quality.',
      rating: 5,
      avatar: '/api/placeholder/64/64',
    },
    {
      id: 5,
      name: '<PERSON>',
      role: 'Brand Designer',
      company: 'Creative Agency',
      content: 'Fantastic collection of brand templates. They\'re modern, versatile, and save so much time. The quality is consistently excellent.',
      rating: 5,
      avatar: '/api/placeholder/64/64',
    },
    {
      id: 6,
      name: 'James Miller',
      role: 'Real Estate Developer',
      company: 'Miller Properties',
      content: 'We use these house plans for our residential developments. The designs are market-tested and our buyers love them. Great investment.',
      rating: 5,
      avatar: '/api/placeholder/64/64',
    },
  ];

  return (
    <section className="py-16 bg-white">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">What Our Customers Say</h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Join thousands of satisfied customers who trust Luyari Designs for their creative projects
          </p>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-1 sm:grid-cols-4 gap-8 mb-16 text-center">
          <div>
            <div className="text-3xl font-bold text-blue-600">50,000+</div>
            <div className="text-sm text-gray-600 mt-1">Happy Customers</div>
          </div>
          <div>
            <div className="text-3xl font-bold text-blue-600">5,000+</div>
            <div className="text-sm text-gray-600 mt-1">Design Templates</div>
          </div>
          <div>
            <div className="text-3xl font-bold text-blue-600">4.9/5</div>
            <div className="text-sm text-gray-600 mt-1">Average Rating</div>
          </div>
          <div>
            <div className="text-3xl font-bold text-blue-600">99%</div>
            <div className="text-sm text-gray-600 mt-1">Satisfaction Rate</div>
          </div>
        </div>

        {/* Testimonials Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {testimonials.map((testimonial) => (
            <div
              key={testimonial.id}
              className="bg-gray-50 rounded-xl p-6 border border-gray-200 hover:shadow-lg transition-shadow"
            >
              {/* Rating */}
              <div className="flex items-center mb-4">
                {[...Array(testimonial.rating)].map((_, i) => (
                  <StarIcon key={i} className="h-5 w-5 text-yellow-400" />
                ))}
              </div>

              {/* Content */}
              <p className="text-gray-700 mb-6 leading-relaxed">
                "{testimonial.content}"
              </p>

              {/* Author */}
              <div className="flex items-center">
                <img
                  src={testimonial.avatar}
                  alt={testimonial.name}
                  className="w-12 h-12 rounded-full object-cover"
                />
                <div className="ml-3">
                  <div className="font-semibold text-gray-900">{testimonial.name}</div>
                  <div className="text-sm text-gray-600">
                    {testimonial.role}
                    {testimonial.company !== 'Independent' && (
                      <span> at {testimonial.company}</span>
                    )}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* CTA */}
        <div className="text-center mt-12">
          <p className="text-lg text-gray-600 mb-6">
            Ready to join our community of satisfied customers?
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="/shop"
              className="inline-flex items-center justify-center px-8 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors"
            >
              Start Shopping
            </a>
            <a
              href="/about"
              className="inline-flex items-center justify-center px-8 py-3 border border-gray-300 text-gray-700 font-medium rounded-lg hover:bg-gray-50 transition-colors"
            >
              Learn More
            </a>
          </div>
        </div>
      </div>
    </section>
  );
}
