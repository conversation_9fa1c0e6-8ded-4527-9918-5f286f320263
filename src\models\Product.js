import mongoose from 'mongoose';

const ProductSchema = new mongoose.Schema({
  title: {
    type: String,
    required: true,
  },
  description: {
    type: String,
    required: true,
  },
  shortDescription: {
    type: String,
    required: true,
  },
  price: {
    type: Number,
    required: true,
  },
  salePrice: {
    type: Number,
  },
  category: {
    type: String,
    required: true,
    enum: ['house-plans', 'templates', 'graphics', 'ui-kits', 'mockups', 'fonts'],
  },
  subcategory: {
    type: String,
  },
  tags: [{
    type: String,
  }],
  images: [{
    url: String,
    alt: String,
    isPrimary: {
      type: Boolean,
      default: false,
    },
  }],
  files: [{
    name: String,
    url: String,
    type: String, // 'pdf', 'dwg', 'psd', 'ai', etc.
    size: Number, // in bytes
  }],
  specifications: {
    dimensions: {
      width: Number,
      height: Number,
      depth: Number,
      unit: {
        type: String,
        default: 'm',
      },
    },
    area: {
      value: Number,
      unit: {
        type: String,
        default: 'sqm',
      },
    },
    bedrooms: Number,
    bathrooms: Number,
    floors: Number,
    style: String,
    features: [String],
  },
  status: {
    type: String,
    enum: ['draft', 'published', 'archived'],
    default: 'draft',
  },
  featured: {
    type: Boolean,
    default: false,
  },
  bestseller: {
    type: Boolean,
    default: false,
  },
  downloadCount: {
    type: Number,
    default: 0,
  },
  rating: {
    average: {
      type: Number,
      default: 0,
    },
    count: {
      type: Number,
      default: 0,
    },
  },
  seo: {
    metaTitle: String,
    metaDescription: String,
    slug: {
      type: String,
      unique: true,
      required: true,
    },
  },
}, {
  timestamps: true,
});

// Create index for search
ProductSchema.index({ title: 'text', description: 'text', tags: 'text' });
ProductSchema.index({ category: 1, status: 1 });
ProductSchema.index({ featured: 1, status: 1 });
ProductSchema.index({ bestseller: 1, status: 1 });

export default mongoose.models.Product || mongoose.model('Product', ProductSchema);
