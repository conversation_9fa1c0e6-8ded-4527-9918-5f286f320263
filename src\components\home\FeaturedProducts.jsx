'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { useCart } from '@/components/providers/CartProvider';
import { toast } from '@/components/ui/Toaster';
import { ShoppingCartIcon, EyeIcon } from '@heroicons/react/24/outline';

export default function FeaturedProducts() {
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const { addItem } = useCart();

  useEffect(() => {
    // Mock data for now - replace with actual API call
    const mockProducts = [
      {
        id: '1',
        title: 'Modern 4-Bedroom House Plan',
        shortDescription: 'Contemporary design with open floor plan',
        price: 299,
        salePrice: 199,
        category: 'house-plans',
        images: [{ url: '/api/placeholder/400/300', alt: 'Modern House Plan', isPrimary: true }],
        specifications: { bedrooms: 4, bathrooms: 3, area: { value: 250, unit: 'sqm' } },
        featured: true,
        bestseller: true,
      },
      {
        id: '2',
        title: 'UI Kit - Dashboard Design',
        shortDescription: 'Complete dashboard UI components',
        price: 89,
        category: 'ui-kits',
        images: [{ url: '/api/placeholder/400/300', alt: 'UI Kit', isPrimary: true }],
        featured: true,
      },
      {
        id: '3',
        title: 'Luxury Villa Blueprint',
        shortDescription: '5-bedroom luxury villa with pool',
        price: 599,
        salePrice: 449,
        category: 'house-plans',
        images: [{ url: '/api/placeholder/400/300', alt: 'Luxury Villa', isPrimary: true }],
        specifications: { bedrooms: 5, bathrooms: 4, area: { value: 450, unit: 'sqm' } },
        featured: true,
      },
      {
        id: '4',
        title: 'Brand Identity Template',
        shortDescription: 'Complete branding package',
        price: 149,
        category: 'templates',
        images: [{ url: '/api/placeholder/400/300', alt: 'Brand Identity', isPrimary: true }],
        featured: true,
      },
    ];

    setTimeout(() => {
      setProducts(mockProducts);
      setLoading(false);
    }, 1000);
  }, []);

  const handleAddToCart = (product) => {
    addItem({
      id: product.id,
      title: product.title,
      price: product.price,
      salePrice: product.salePrice,
      image: product.images[0]?.url,
    });
    toast.success('Added to cart!');
  };

  if (loading) {
    return (
      <section className="py-16 bg-white">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900">Featured Products</h2>
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="animate-pulse">
                <div className="bg-gray-200 h-48 rounded-lg mb-4"></div>
                <div className="h-4 bg-gray-200 rounded mb-2"></div>
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
              </div>
            ))}
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className="py-16 bg-white">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">Featured Products</h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Discover our most popular and highest-rated design templates
          </p>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
          {products.map((product) => (
            <div key={product.id} className="group relative bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-lg transition-shadow">
              {/* Product Image */}
              <div className="relative overflow-hidden rounded-t-lg">
                <img
                  src={product.images[0]?.url}
                  alt={product.images[0]?.alt}
                  className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                />
                {product.bestseller && (
                  <span className="absolute top-2 left-2 bg-red-500 text-white text-xs px-2 py-1 rounded">
                    Bestseller
                  </span>
                )}
                {product.salePrice && (
                  <span className="absolute top-2 right-2 bg-green-500 text-white text-xs px-2 py-1 rounded">
                    Sale
                  </span>
                )}
                
                {/* Hover Actions */}
                <div className="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center space-x-2">
                  <Link
                    href={`/products/${product.id}`}
                    className="bg-white text-gray-900 p-2 rounded-full hover:bg-gray-100 transition-colors"
                  >
                    <EyeIcon className="h-5 w-5" />
                  </Link>
                  <button
                    onClick={() => handleAddToCart(product)}
                    className="bg-blue-600 text-white p-2 rounded-full hover:bg-blue-700 transition-colors"
                  >
                    <ShoppingCartIcon className="h-5 w-5" />
                  </button>
                </div>
              </div>

              {/* Product Info */}
              <div className="p-4">
                <h3 className="font-semibold text-gray-900 mb-2 line-clamp-2">
                  {product.title}
                </h3>
                <p className="text-sm text-gray-600 mb-3 line-clamp-2">
                  {product.shortDescription}
                </p>
                
                {/* Specifications for house plans */}
                {product.category === 'house-plans' && product.specifications && (
                  <div className="flex text-xs text-gray-500 space-x-3 mb-3">
                    {product.specifications.bedrooms && (
                      <span>{product.specifications.bedrooms} bed</span>
                    )}
                    {product.specifications.bathrooms && (
                      <span>{product.specifications.bathrooms} bath</span>
                    )}
                    {product.specifications.area && (
                      <span>{product.specifications.area.value} {product.specifications.area.unit}</span>
                    )}
                  </div>
                )}

                {/* Price */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    {product.salePrice ? (
                      <>
                        <span className="text-lg font-bold text-gray-900">
                          ${product.salePrice}
                        </span>
                        <span className="text-sm text-gray-500 line-through">
                          ${product.price}
                        </span>
                      </>
                    ) : (
                      <span className="text-lg font-bold text-gray-900">
                        ${product.price}
                      </span>
                    )}
                  </div>
                  <button
                    onClick={() => handleAddToCart(product)}
                    className="text-blue-600 hover:text-blue-700 transition-colors"
                  >
                    <ShoppingCartIcon className="h-5 w-5" />
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="text-center mt-12">
          <Link
            href="/shop"
            className="inline-flex items-center px-6 py-3 border border-gray-300 text-gray-700 font-medium rounded-lg hover:bg-gray-50 transition-colors"
          >
            View All Products
          </Link>
        </div>
      </div>
    </section>
  );
}
