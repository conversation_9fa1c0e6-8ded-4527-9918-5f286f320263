# Luyari Designs - E-commerce Platform

A comprehensive Next.js e-commerce platform for selling design templates, house plans, and digital assets. Inspired by Maramani.com, this platform provides a complete solution for digital product sales with modern authentication, payment processing, and admin management.

## Features

### 🔐 Authentication System
- **Multiple Auth Providers**: Google, Facebook, Apple, and Magic Link (email-based passwordless)
- **Secure Sessions**: Database-backed sessions with Auth.js
- **Role-based Access**: User and admin roles with protected routes

### 💳 Payment Processing
- **Stripe Integration**: Secure payment processing with Stripe Checkout
- **Order Management**: Complete order tracking and management system
- **Digital Downloads**: Secure file delivery after purchase

### 🗄️ Database & Storage
- **MongoDB**: Robust data storage with Mongoose ODM
- **Firebase Storage**: Secure file storage for design assets
- **Optimized Queries**: Efficient data retrieval with proper indexing

### 🛍️ E-commerce Features
- **Product Catalog**: Comprehensive product management system
- **Shopping Cart**: Persistent cart with local storage
- **Categories & Filtering**: Advanced product filtering and search
- **Responsive Design**: Mobile-first design with Tailwind CSS

### 👨‍💼 Admin Dashboard
- **Product Management**: Add, edit, and manage products
- **Order Management**: Track and manage customer orders
- **User Management**: Manage customer accounts
- **Analytics**: Sales reports and performance metrics

## Tech Stack

- **Frontend**: Next.js 15, React 19, Tailwind CSS
- **Authentication**: Auth.js (NextAuth.js) v5
- **Database**: MongoDB with Mongoose
- **File Storage**: Firebase Storage
- **Payments**: Stripe
- **Email**: Nodemailer for transactional emails
- **Icons**: Heroicons
- **Deployment**: Vercel-ready

## Getting Started

### Prerequisites

- Node.js 18+
- MongoDB database (local or MongoDB Atlas)
- Firebase project for file storage
- Stripe account for payments
- Email service (Gmail, SendGrid, etc.)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd luyaridesignstemplate
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Environment Setup**

   Copy `.env.local` and configure your environment variables:

   ```bash
   # Database
   MONGODB_URI=mongodb://localhost:27017/luyari-designs

   # Auth.js
   NEXTAUTH_URL=http://localhost:3000
   NEXTAUTH_SECRET=your-secret-key-here

   # OAuth Providers
   GOOGLE_CLIENT_ID=your-google-client-id
   GOOGLE_CLIENT_SECRET=your-google-client-secret
   FACEBOOK_CLIENT_ID=your-facebook-app-id
   FACEBOOK_CLIENT_SECRET=your-facebook-app-secret

   # Email
   EMAIL_SERVER_HOST=smtp.gmail.com
   EMAIL_SERVER_PORT=587
   EMAIL_SERVER_USER=<EMAIL>
   EMAIL_SERVER_PASSWORD=your-app-password
   EMAIL_FROM=<EMAIL>

   # Stripe
   STRIPE_PUBLISHABLE_KEY=pk_test_your-stripe-publishable-key
   STRIPE_SECRET_KEY=sk_test_your-stripe-secret-key

   # Firebase
   NEXT_PUBLIC_FIREBASE_API_KEY=your-firebase-api-key
   NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your-project.firebaseapp.com
   NEXT_PUBLIC_FIREBASE_PROJECT_ID=your-project-id
   NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your-project.appspot.com

   # Admin
   ADMIN_EMAIL=<EMAIL>
   ```

4. **Run the development server**
   ```bash
   npm run dev
   ```

5. **Open your browser**

   Navigate to [http://localhost:3000](http://localhost:3000)

## Quick Start (Development)

For a quick development setup without external services:

1. **Clone and install**
   ```bash
   git clone <repository-url>
   cd luyaridesignstemplate
   npm install
   ```

2. **Start development server**
   ```bash
   npm run dev
   ```

3. **View the application**

   Open [http://localhost:3000](http://localhost:3000)

The application will work with mock data and placeholder images. Authentication and database features will require proper configuration.

## Database Setup

### MongoDB Atlas (Recommended for Production)

1. Create a MongoDB Atlas account at [mongodb.com](https://www.mongodb.com/atlas)
2. Create a new cluster
3. Get your connection string
4. Update `MONGODB_URI` in `.env.local`
5. Seed the database:
   ```bash
   npm run seed
   ```

### Local MongoDB (Development)

1. Install MongoDB locally
2. Start MongoDB service
3. Update `MONGODB_URI=mongodb://localhost:27017/luyari-designs`
4. Seed the database:
   ```bash
   npm run seed
   ```
