{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/middleware.js"], "sourcesContent": ["import { withAuth } from 'next-auth/middleware';\n\nexport default withAuth(\n  function middleware(req) {\n    // Add any additional middleware logic here\n  },\n  {\n    callbacks: {\n      authorized: ({ token, req }) => {\n        // Allow access to public routes\n        if (req.nextUrl.pathname.startsWith('/admin')) {\n          return token?.role === 'admin';\n        }\n        \n        // Allow access to protected user routes\n        if (req.nextUrl.pathname.startsWith('/account')) {\n          return !!token;\n        }\n        \n        return true;\n      },\n    },\n  }\n);\n\nexport const config = {\n  matcher: [\n    '/admin/:path*',\n    '/account/:path*',\n  ],\n};\n"], "names": [], "mappings": ";;;;AAAA;;uCAEe,CAAA,GAAA,kJAAA,CAAA,WAAQ,AAAD,EACpB,SAAS,WAAW,GAAG;AACrB,2CAA2C;AAC7C,GACA;IACE,WAAW;QACT,YAAY,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE;YACzB,gCAAgC;YAChC,IAAI,IAAI,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,WAAW;gBAC7C,OAAO,OAAO,SAAS;YACzB;YAEA,wCAAwC;YACxC,IAAI,IAAI,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,aAAa;gBAC/C,OAAO,CAAC,CAAC;YACX;YAEA,OAAO;QACT;IACF;AACF;AAGK,MAAM,SAAS;IACpB,SAAS;QACP;QACA;KACD;AACH"}}]}