"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("@react-three/fiber"),t=require("react"),r=require("three"),n=require("meshline");function o(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var n=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,n.get?n:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,Object.freeze(t)}var i=o(t);const u={width:.2,length:1,decay:1,local:!1,stride:0,interval:1},s=(e,t=1)=>(e.set(e.subarray(t)),e.fill(-1/0,-t),e);function c(t,n){const{length:o,local:c,decay:l,interval:a,stride:f}={...u,...n},d=i.useRef(null),[h]=i.useState((()=>new r.Vector3));i.useLayoutEffect((()=>{t&&(d.current=Float32Array.from({length:10*o*3},((e,r)=>t.position.getComponent(r%3))))}),[o,t]);const p=i.useRef(new r.Vector3),y=i.useRef(0);return e.useFrame((()=>{if(t&&d.current){if(0===y.current){let e;c?e=t.position:(t.getWorldPosition(h),e=h);const r=1*l;for(let t=0;t<r;t++)e.distanceTo(p.current)<f||(s(d.current,3),d.current.set(e.toArray(),d.current.length-3));p.current.copy(e)}y.current++,y.current=y.current%a}})),d}const l=i.forwardRef(((t,o)=>{const{children:s}=t,{width:l,length:a,decay:f,local:d,stride:h,interval:p}={...u,...t},{color:y="hotpink",attenuation:g,target:m}=t,v=e.useThree((e=>e.size)),b=e.useThree((e=>e.scene)),w=i.useRef(null),[M,j]=i.useState(null),O=c(M,{length:a,decay:f,local:d,stride:h,interval:p});i.useEffect((()=>{const e=(null==m?void 0:m.current)||w.current.children.find((e=>e instanceof r.Object3D));e&&j(e)}),[O,m]);const E=i.useMemo((()=>new n.MeshLineGeometry),[]),P=i.useMemo((()=>{var e,t;const o=new n.MeshLineMaterial({lineWidth:.1*l,color:y,sizeAttenuation:1,resolution:new r.Vector2(v.width,v.height)});let i;if(s)if(Array.isArray(s))i=s.find((e=>{const t=e;return"string"==typeof t.type&&"meshLineMaterial"===t.type}));else{const e=s;"string"==typeof e.type&&"meshLineMaterial"===e.type&&(i=e)}return"object"==typeof(null==(e=i)?void 0:e.props)&&null!==(null==(t=i)?void 0:t.props)&&o.setValues(i.props),o}),[l,y,v,s]);return i.useEffect((()=>{P.uniforms.resolution.value.set(v.width,v.height)}),[v]),e.useFrame((()=>{O.current&&E.setPoints(O.current,g)})),i.createElement("group",null,e.createPortal(i.createElement("mesh",{ref:o,geometry:E,material:P}),b),i.createElement("group",{ref:w},s))}));exports.Trail=l,exports.useTrail=c;
