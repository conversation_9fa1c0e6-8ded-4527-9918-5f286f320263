import { NextResponse } from 'next/server';
import connectDB from '@/lib/mongoose';
import Wishlist from '@/models/Wishlist';

export async function GET(request, { params }) {
  try {
    await connectDB();
    
    const resolvedParams = await params;
    const token = resolvedParams.token;
    
    const wishlist = await Wishlist.findOne({ 
      shareToken: token,
      isPublic: true,
    })
    .populate('user', 'name')
    .populate({
      path: 'items.product',
      select: 'title price salePrice images category status description',
    });
    
    if (!wishlist) {
      return NextResponse.json(
        { error: 'Shared wishlist not found' },
        { status: 404 }
      );
    }

    // Filter out unavailable products
    const availableItems = wishlist.items.filter(
      item => item.product && item.product.status === 'published'
    );

    const response = {
      _id: wishlist._id,
      name: wishlist.name,
      description: wishlist.description,
      owner: wishlist.user.name,
      items: availableItems,
      itemCount: availableItems.length,
      sharedAt: wishlist.sharedAt,
    };
    
    return NextResponse.json(response);
  } catch (error) {
    console.error('Error fetching shared wishlist:', error);
    return NextResponse.json(
      { error: 'Failed to fetch shared wishlist' },
      { status: 500 }
    );
  }
}
