# Deployment Guide - <PERSON><PERSON><PERSON> Designs E-commerce Platform

## Overview

This guide covers deploying the Luyari Designs e-commerce platform to production. The application is built with Next.js and requires several external services.

## Prerequisites

- Node.js 18+
- MongoDB database (Atlas recommended)
- Firebase project for file storage
- Stripe account for payments
- Email service (Gmail, SendGrid, etc.)
- OAuth app credentials (Google, Facebook, Apple)

## Environment Variables

Create a `.env.local` file with the following variables:

```bash
# Database
MONGODB_URI=mongodb+srv://username:<EMAIL>/luyari-designs

# Auth.js
NEXTAUTH_URL=https://yourdomain.com
NEXTAUTH_SECRET=your-production-secret-key

# OAuth Providers
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
FACEBOOK_CLIENT_ID=your-facebook-app-id
FACEBOOK_CLIENT_SECRET=your-facebook-app-secret
APPLE_ID=your-apple-service-id
APPLE_TEAM_ID=your-apple-team-id
APPLE_PRIVATE_KEY=your-apple-private-key
APPLE_KEY_ID=your-apple-key-id

# Email
EMAIL_SERVER_HOST=smtp.gmail.com
EMAIL_SERVER_PORT=587
EMAIL_SERVER_USER=<EMAIL>
EMAIL_SERVER_PASSWORD=your-app-password
EMAIL_FROM=<EMAIL>

# Stripe
STRIPE_PUBLISHABLE_KEY=pk_live_your-stripe-publishable-key
STRIPE_SECRET_KEY=sk_live_your-stripe-secret-key
STRIPE_WEBHOOK_SECRET=whsec_your-webhook-secret

# Firebase
NEXT_PUBLIC_FIREBASE_API_KEY=your-firebase-api-key
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your-project.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your-project-id
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your-project.appspot.com
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your-sender-id
NEXT_PUBLIC_FIREBASE_APP_ID=your-app-id

# Admin
ADMIN_EMAIL=<EMAIL>
```

## Vercel Deployment (Recommended)

1. **Push to GitHub**
   ```bash
   git add .
   git commit -m "Initial commit"
   git push origin main
   ```

2. **Connect to Vercel**
   - Go to [vercel.com](https://vercel.com)
   - Import your GitHub repository
   - Configure environment variables in Vercel dashboard

3. **Deploy**
   - Vercel will automatically deploy on push
   - Set up custom domain if needed

## Database Setup

### MongoDB Atlas

1. Create account at [mongodb.com/atlas](https://www.mongodb.com/atlas)
2. Create new cluster
3. Create database user
4. Whitelist IP addresses (0.0.0.0/0 for all IPs)
5. Get connection string
6. Update `MONGODB_URI` in environment variables

### Seed Database

```bash
npm run seed
```

## Firebase Storage Setup

1. Create Firebase project at [console.firebase.google.com](https://console.firebase.google.com)
2. Enable Storage
3. Configure security rules:

```javascript
rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    match /products/{allPaths=**} {
      allow read: if true;
      allow write: if request.auth != null;
    }
    match /uploads/{userId}/{allPaths=**} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
  }
}
```

## Stripe Setup

1. Create Stripe account
2. Get API keys from dashboard
3. Set up webhooks:
   - Endpoint: `https://yourdomain.com/api/webhooks/stripe`
   - Events: `checkout.session.completed`, `payment_intent.succeeded`

## OAuth Setup

### Google OAuth

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create new project or select existing
3. Enable Google+ API
4. Create OAuth 2.0 credentials
5. Add authorized redirect URIs:
   - `https://yourdomain.com/api/auth/callback/google`

### Facebook OAuth

1. Go to [Facebook Developers](https://developers.facebook.com/)
2. Create new app
3. Add Facebook Login product
4. Configure OAuth redirect URIs:
   - `https://yourdomain.com/api/auth/callback/facebook`

### Apple OAuth

1. Go to [Apple Developer](https://developer.apple.com/)
2. Create App ID and Service ID
3. Configure Sign in with Apple
4. Generate private key

## Email Configuration

### Gmail

1. Enable 2-factor authentication
2. Generate app password
3. Use app password in `EMAIL_SERVER_PASSWORD`

### SendGrid (Alternative)

1. Create SendGrid account
2. Get API key
3. Update email configuration:

```bash
EMAIL_SERVER_HOST=smtp.sendgrid.net
EMAIL_SERVER_PORT=587
EMAIL_SERVER_USER=apikey
EMAIL_SERVER_PASSWORD=your-sendgrid-api-key
```

## Security Checklist

- [ ] Use strong `NEXTAUTH_SECRET` in production
- [ ] Enable HTTPS (automatic with Vercel)
- [ ] Configure CORS if needed
- [ ] Set up rate limiting
- [ ] Configure CSP headers
- [ ] Enable MongoDB IP whitelisting
- [ ] Use environment variables for all secrets
- [ ] Set up monitoring and logging

## Performance Optimization

- [ ] Enable Next.js Image Optimization
- [ ] Configure CDN for static assets
- [ ] Set up database indexing
- [ ] Enable compression
- [ ] Configure caching headers
- [ ] Monitor Core Web Vitals

## Monitoring

- [ ] Set up error tracking (Sentry)
- [ ] Configure analytics (Google Analytics)
- [ ] Monitor uptime
- [ ] Set up alerts for critical errors
- [ ] Monitor database performance

## Backup Strategy

- [ ] Set up automated MongoDB backups
- [ ] Configure Firebase Storage backups
- [ ] Document recovery procedures
- [ ] Test backup restoration

## Post-Deployment

1. **Test all functionality**
   - Authentication flows
   - Payment processing
   - File uploads/downloads
   - Email notifications

2. **Set up monitoring**
   - Error tracking
   - Performance monitoring
   - Uptime monitoring

3. **Configure analytics**
   - Google Analytics
   - Conversion tracking
   - User behavior analysis

4. **SEO optimization**
   - Submit sitemap to search engines
   - Configure meta tags
   - Set up structured data

## Troubleshooting

### Common Issues

1. **MongoDB Connection Errors**
   - Check connection string
   - Verify IP whitelist
   - Check database user permissions

2. **Auth.js Issues**
   - Verify OAuth redirect URIs
   - Check environment variables
   - Ensure NEXTAUTH_URL is correct

3. **Stripe Webhook Failures**
   - Verify webhook endpoint
   - Check webhook secret
   - Monitor webhook logs

4. **File Upload Issues**
   - Check Firebase security rules
   - Verify storage bucket configuration
   - Monitor Firebase usage

## Support

For deployment issues:
- Check Next.js documentation
- Review Vercel deployment logs
- Monitor application logs
- Contact support if needed
