import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import connectDB from '@/lib/mongoose';
import Review from '@/models/Review';

export async function GET(request, { params }) {
  try {
    await connectDB();
    
    const resolvedParams = await params;
    const review = await Review.findById(resolvedParams.id)
      .populate('user', 'name image')
      .populate('product', 'title')
      .lean();
    
    if (!review) {
      return NextResponse.json(
        { error: 'Review not found' },
        { status: 404 }
      );
    }
    
    // Calculate helpful score
    const helpfulScore = review.helpful ? 
      review.helpful.filter(h => h.helpful).length - 
      review.helpful.filter(h => !h.helpful).length : 0;
    
    return NextResponse.json({
      ...review,
      helpfulScore,
    });
  } catch (error) {
    console.error('Error fetching review:', error);
    return NextResponse.json(
      { error: 'Failed to fetch review' },
      { status: 500 }
    );
  }
}

export async function PUT(request, { params }) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    await connectDB();
    
    const resolvedParams = await params;
    const { rating, title, comment, images, status, moderatorNotes } = await request.json();
    
    const review = await Review.findById(resolvedParams.id);
    
    if (!review) {
      return NextResponse.json(
        { error: 'Review not found' },
        { status: 404 }
      );
    }

    // Check permissions
    const isOwner = review.user.toString() === session.user.id;
    const isAdmin = session.user.role === 'admin';
    
    if (!isOwner && !isAdmin) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 403 }
      );
    }

    // Update fields based on user role
    if (isOwner && !isAdmin) {
      // Regular users can only update their own review content
      if (rating !== undefined) review.rating = rating;
      if (title !== undefined) review.title = title;
      if (comment !== undefined) review.comment = comment;
      if (images !== undefined) review.images = images;
      
      // Reset status to pending if content changed
      if (rating !== undefined || title !== undefined || comment !== undefined) {
        review.status = 'pending';
      }
    }
    
    if (isAdmin) {
      // Admins can update status and moderator notes
      if (status !== undefined) review.status = status;
      if (moderatorNotes !== undefined) review.moderatorNotes = moderatorNotes;
    }

    await review.save();
    await review.populate('user', 'name image');
    
    return NextResponse.json(review);
  } catch (error) {
    console.error('Error updating review:', error);
    return NextResponse.json(
      { error: 'Failed to update review' },
      { status: 500 }
    );
  }
}

export async function DELETE(request, { params }) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    await connectDB();
    
    const resolvedParams = await params;
    const review = await Review.findById(resolvedParams.id);
    
    if (!review) {
      return NextResponse.json(
        { error: 'Review not found' },
        { status: 404 }
      );
    }

    // Check permissions
    const isOwner = review.user.toString() === session.user.id;
    const isAdmin = session.user.role === 'admin';
    
    if (!isOwner && !isAdmin) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 403 }
      );
    }

    await Review.findByIdAndDelete(resolvedParams.id);
    
    return NextResponse.json({ message: 'Review deleted successfully' });
  } catch (error) {
    console.error('Error deleting review:', error);
    return NextResponse.json(
      { error: 'Failed to delete review' },
      { status: 500 }
    );
  }
}
