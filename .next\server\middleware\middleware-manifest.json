{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_c54a1250._.js", "server/edge/chunks/[root-of-the-server]__e07e566a._.js", "server/edge/chunks/edge-wrapper_60e6f275.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/admin/:path*{(\\\\.json)}?", "originalSource": "/admin/:path*"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/account/:path*{(\\\\.json)}?", "originalSource": "/account/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "0KKkVeSHVNC/o+yUjjzzeS/zBOKtzlGv/jpVHdLq4KE=", "__NEXT_PREVIEW_MODE_ID": "2b9df5730493a5739cd3ea2073ebea50", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "9b11011a0827cb4d51b92fddc273cc69b35f59f234853f6a8800f76e00ac2553", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "5c62739251b0043a9726a74db53b23196f8564d6a4fa9401d298e410e68b1b6b"}}}, "instrumentation": null, "functions": {}}