'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import Link from 'next/link';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import { useCart } from '@/components/providers/CartProvider';
import { toast } from '@/components/ui/Toaster';
import { StarDisplay } from '@/components/ui/StarRating';
import WishlistButton from '@/components/wishlist/WishlistButton';
import { 
  ShoppingCartIcon, 
  HeartIcon,
  ShareIcon,
  CalendarIcon 
} from '@heroicons/react/24/outline';

export default function SharedWishlistPage() {
  const params = useParams();
  const { addItem } = useCart();
  const [wishlist, setWishlist] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    if (params.token) {
      fetchSharedWishlist();
    }
  }, [params.token]);

  const fetchSharedWishlist = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/wishlist/shared/${params.token}`);
      
      if (response.ok) {
        const data = await response.json();
        setWishlist(data);
      } else if (response.status === 404) {
        setError('Wishlist not found or no longer shared');
      } else {
        setError('Failed to load wishlist');
      }
    } catch (error) {
      console.error('Error fetching shared wishlist:', error);
      setError('Something went wrong');
    } finally {
      setLoading(false);
    }
  };

  const handleAddToCart = (product) => {
    addItem({
      id: product._id,
      title: product.title,
      price: product.price,
      salePrice: product.salePrice,
      image: product.images?.[0]?.url,
    });
    toast.success('Added to cart!');
  };

  const handleShare = async () => {
    try {
      await navigator.clipboard.writeText(window.location.href);
      toast.success('Link copied to clipboard!');
    } catch (error) {
      toast.error('Failed to copy link');
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-white">
        <Header />
        <main className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-16">
          <div className="animate-pulse space-y-4">
            <div className="h-8 bg-gray-200 rounded w-1/3"></div>
            <div className="h-4 bg-gray-200 rounded w-1/4"></div>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 mt-8">
              {[...Array(6)].map((_, i) => (
                <div key={i} className="bg-gray-200 h-64 rounded-lg"></div>
              ))}
            </div>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-white">
        <Header />
        <main className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-16">
          <div className="text-center">
            <HeartIcon className="mx-auto h-24 w-24 text-gray-300 mb-4" />
            <h1 className="text-2xl font-bold text-gray-900 mb-2">Wishlist Not Found</h1>
            <p className="text-gray-600 mb-8">{error}</p>
            <Link
              href="/shop"
              className="inline-flex items-center px-6 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors"
            >
              Browse Products
            </Link>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white">
      <Header />
      
      <main className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg p-8 mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">
                {wishlist.name}
              </h1>
              <p className="text-lg text-gray-600 mb-2">
                Shared by {wishlist.owner}
              </p>
              {wishlist.description && (
                <p className="text-gray-600 mb-4">{wishlist.description}</p>
              )}
              <div className="flex items-center space-x-4 text-sm text-gray-500">
                <div className="flex items-center space-x-1">
                  <HeartIcon className="h-4 w-4" />
                  <span>{wishlist.itemCount} item{wishlist.itemCount !== 1 ? 's' : ''}</span>
                </div>
                <div className="flex items-center space-x-1">
                  <CalendarIcon className="h-4 w-4" />
                  <span>Shared {new Date(wishlist.sharedAt).toLocaleDateString()}</span>
                </div>
              </div>
            </div>
            
            <button
              onClick={handleShare}
              className="flex items-center space-x-2 px-4 py-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <ShareIcon className="h-4 w-4" />
              <span>Share</span>
            </button>
          </div>
        </div>

        {/* Wishlist Items */}
        {wishlist.itemCount === 0 ? (
          <div className="text-center py-16">
            <HeartIcon className="mx-auto h-24 w-24 text-gray-300 mb-4" />
            <h2 className="text-2xl font-semibold text-gray-900 mb-2">This wishlist is empty</h2>
            <p className="text-gray-600 mb-8">
              {wishlist.owner} hasn't added any items to this wishlist yet.
            </p>
          </div>
        ) : (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {wishlist.items.map((item) => (
              <div key={item._id} className="group relative bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-lg transition-shadow">
                {/* Product Image */}
                <div className="relative overflow-hidden rounded-t-lg">
                  <Link href={`/products/${item.product._id}`}>
                    <img
                      src={item.product.images?.[0]?.url || '/api/placeholder/400/300'}
                      alt={item.product.title}
                      className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                    />
                  </Link>
                  
                  {/* Sale Badge */}
                  {item.product.salePrice && (
                    <span className="absolute top-2 left-2 bg-green-500 text-white text-xs px-2 py-1 rounded">
                      Sale
                    </span>
                  )}
                  
                  {/* Wishlist Button */}
                  <div className="absolute top-2 right-2">
                    <WishlistButton
                      productId={item.product._id}
                      size="md"
                      className="p-2 bg-white rounded-full shadow-sm hover:shadow-md"
                    />
                  </div>
                </div>

                {/* Product Info */}
                <div className="p-4">
                  <Link href={`/products/${item.product._id}`}>
                    <h3 className="font-semibold text-gray-900 mb-2 line-clamp-2 hover:text-blue-600 transition-colors">
                      {item.product.title}
                    </h3>
                  </Link>
                  
                  {/* Description */}
                  {item.product.description && (
                    <p className="text-sm text-gray-600 mb-2 line-clamp-2">
                      {item.product.description}
                    </p>
                  )}
                  
                  {/* Rating */}
                  {item.product.rating && item.product.rating.count > 0 && (
                    <div className="mb-2">
                      <StarDisplay 
                        rating={item.product.rating.average} 
                        count={item.product.rating.count}
                        size="sm" 
                      />
                    </div>
                  )}
                  
                  {/* Price */}
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-2">
                      {item.product.salePrice ? (
                        <>
                          <span className="text-lg font-bold text-gray-900">
                            ${item.product.salePrice}
                          </span>
                          <span className="text-sm text-gray-500 line-through">
                            ${item.product.price}
                          </span>
                        </>
                      ) : (
                        <span className="text-lg font-bold text-gray-900">
                          ${item.product.price}
                        </span>
                      )}
                    </div>
                  </div>
                  
                  {/* Actions */}
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => handleAddToCart(item.product)}
                      className="flex-1 bg-blue-600 text-white px-3 py-2 rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium flex items-center justify-center space-x-1"
                    >
                      <ShoppingCartIcon className="h-4 w-4" />
                      <span>Add to Cart</span>
                    </button>
                    
                    <Link
                      href={`/products/${item.product._id}`}
                      className="px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors text-sm font-medium"
                    >
                      View
                    </Link>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* CTA Section */}
        <div className="mt-16 bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl p-8 text-center text-white">
          <h3 className="text-2xl font-bold mb-4">Create Your Own Wishlist</h3>
          <p className="text-lg opacity-90 mb-6 max-w-2xl mx-auto">
            Save your favorite products and share them with friends and family. 
            Sign up to start building your wishlist today.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/auth/signin"
              className="inline-flex items-center justify-center px-6 py-3 bg-white text-blue-600 font-medium rounded-lg hover:bg-gray-100 transition-colors"
            >
              Sign Up / Sign In
            </Link>
            <Link
              href="/shop"
              className="inline-flex items-center justify-center px-6 py-3 border border-white text-white font-medium rounded-lg hover:bg-white hover:text-blue-600 transition-colors"
            >
              Browse Products
            </Link>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
}
