// Optional Sentry import - only load if package is installed
let Sentry = null;
try {
  Sentry = require('@sentry/nextjs');
} catch (error) {
  console.warn('Sentry not installed. Error tracking will be disabled.');
}

const SENTRY_DSN = process.env.NEXT_PUBLIC_SENTRY_DSN;

export const initSentry = () => {
  if (SENTRY_DSN && Sentry) {
    Sentry.init({
      dsn: SENTRY_DSN,
      environment: process.env.NODE_ENV,
      tracesSampleRate: process.env.NODE_ENV === 'production' ? 0.1 : 1.0,
      debug: process.env.NODE_ENV === 'development',

      // Performance monitoring
      integrations: [
        new Sentry.BrowserTracing({
          // Set sampling rate for performance monitoring
          tracePropagationTargets: [
            'localhost',
            /^https:\/\/yourapp\.com\/api/,
          ],
        }),
      ],

      // Error filtering
      beforeSend(event, hint) {
        // Filter out known non-critical errors
        const error = hint.originalException;

        if (error && error.message) {
          // Filter out network errors that are not actionable
          if (error.message.includes('Network Error') ||
              error.message.includes('Failed to fetch')) {
            return null;
          }

          // Filter out authentication errors (handled by the app)
          if (error.message.includes('Authentication required') ||
              error.message.includes('Unauthorized')) {
            return null;
          }
        }

        return event;
      },

      // Session tracking
      autoSessionTracking: true,

      // Release tracking
      release: process.env.NEXT_PUBLIC_APP_VERSION || 'unknown',
    });
  }
};

// Custom error tracking functions
export const captureException = (error, context = {}) => {
  if (SENTRY_DSN && Sentry) {
    Sentry.withScope((scope) => {
      // Add context information
      Object.keys(context).forEach(key => {
        scope.setContext(key, context[key]);
      });

      Sentry.captureException(error);
    });
  } else {
    // Fallback to console in development
    console.error('Error captured:', error, context);
  }
};

export const captureMessage = (message, level = 'info', context = {}) => {
  if (SENTRY_DSN && Sentry) {
    Sentry.withScope((scope) => {
      scope.setLevel(level);

      Object.keys(context).forEach(key => {
        scope.setContext(key, context[key]);
      });

      Sentry.captureMessage(message);
    });
  } else {
    console.log(`[${level.toUpperCase()}] ${message}`, context);
  }
};

export const setUserContext = (user) => {
  if (SENTRY_DSN && Sentry) {
    Sentry.setUser({
      id: user.id,
      email: user.email,
      username: user.name,
    });
  }
};

export const addBreadcrumb = (message, category = 'custom', level = 'info', data = {}) => {
  if (SENTRY_DSN && Sentry) {
    Sentry.addBreadcrumb({
      message,
      category,
      level,
      data,
      timestamp: Date.now() / 1000,
    });
  }
};

// Performance monitoring
export const startTransaction = (name, op = 'navigation') => {
  if (SENTRY_DSN && Sentry) {
    return Sentry.startTransaction({ name, op });
  }
  return null;
};

export const finishTransaction = (transaction) => {
  if (transaction) {
    transaction.finish();
  }
};

// E-commerce specific tracking
export const trackCheckoutError = (error, step, items) => {
  captureException(error, {
    checkout: {
      step,
      items: items.map(item => ({
        id: item.id,
        title: item.title,
        price: item.price,
      })),
    },
  });
};

export const trackPaymentError = (error, paymentMethod, amount) => {
  captureException(error, {
    payment: {
      method: paymentMethod,
      amount,
    },
  });
};

export const trackAPIError = (error, endpoint, method, statusCode) => {
  captureException(error, {
    api: {
      endpoint,
      method,
      statusCode,
    },
  });
};

// Database error tracking
export const trackDatabaseError = (error, operation, collection) => {
  captureException(error, {
    database: {
      operation,
      collection,
    },
  });
};

// Authentication error tracking
export const trackAuthError = (error, provider, action) => {
  captureMessage(`Authentication error: ${error.message}`, 'warning', {
    auth: {
      provider,
      action,
      error: error.message,
    },
  });
};
