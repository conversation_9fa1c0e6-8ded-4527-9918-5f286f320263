'use client';

import { useState } from 'react';
import { StarIcon } from '@heroicons/react/24/solid';
import { StarIcon as StarOutlineIcon } from '@heroicons/react/24/outline';

/**
 * Interactive star rating component
 * @param {Object} props - Component props
 * @param {number} props.rating - Current rating value (0-5)
 * @param {Function} props.onRatingChange - Callback when rating changes
 * @param {boolean} props.readonly - Whether the rating is read-only
 * @param {string} props.size - Size of stars ('sm', 'md', 'lg', 'xl')
 * @param {boolean} props.showValue - Whether to show numeric value
 * @param {string} props.className - Additional CSS classes
 * @returns {JSX.Element} Star rating component
 */
export default function StarRating({
  rating = 0,
  onRatingChange,
  readonly = false,
  size = 'md',
  showValue = false,
  className = ''
}) {
  const [hoverRating, setHoverRating] = useState(0);

  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-5 w-5',
    lg: 'h-6 w-6',
    xl: 'h-8 w-8',
  };

  const handleClick = (value) => {
    if (!readonly && onRatingChange) {
      onRatingChange(value);
    }
  };

  const handleMouseEnter = (value) => {
    if (!readonly) {
      setHoverRating(value);
    }
  };

  const handleMouseLeave = () => {
    if (!readonly) {
      setHoverRating(0);
    }
  };

  const displayRating = hoverRating || rating;

  return (
    <div className={`flex items-center space-x-1 ${className}`}>
      <div className="flex items-center">
        {[1, 2, 3, 4, 5].map((star) => {
          const isFilled = star <= displayRating;
          const isPartial = star - 0.5 <= displayRating && star > displayRating;

          return (
            <button
              key={star}
              type="button"
              className={`${
                readonly ? 'cursor-default' : 'cursor-pointer hover:scale-110'
              } transition-transform duration-150 ${
                !readonly ? 'focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 rounded' : ''
              }`}
              onClick={() => handleClick(star)}
              onMouseEnter={() => handleMouseEnter(star)}
              onMouseLeave={handleMouseLeave}
              disabled={readonly}
            >
              {isFilled ? (
                <StarIcon
                  className={`${sizeClasses[size]} text-yellow-400`}
                />
              ) : isPartial ? (
                <div className="relative">
                  <StarOutlineIcon
                    className={`${sizeClasses[size]} text-gray-300`}
                  />
                  <div
                    className="absolute inset-0 overflow-hidden"
                    style={{ width: '50%' }}
                  >
                    <StarIcon
                      className={`${sizeClasses[size]} text-yellow-400`}
                    />
                  </div>
                </div>
              ) : (
                <StarOutlineIcon
                  className={`${sizeClasses[size]} ${
                    readonly ? 'text-gray-300' : 'text-gray-400 hover:text-yellow-400'
                  }`}
                />
              )}
            </button>
          );
        })}
      </div>

      {showValue && (
        <span className="text-sm text-gray-600 ml-2">
          {rating.toFixed(1)}
        </span>
      )}

      {!readonly && hoverRating > 0 && (
        <span className="text-sm text-gray-500 ml-2">
          {hoverRating} star{hoverRating !== 1 ? 's' : ''}
        </span>
      )}
    </div>
  );
}

/**
 * Display-only star rating component
 * @param {Object} props - Component props
 * @param {number} props.rating - Rating value to display (0-5)
 * @param {number} props.count - Number of reviews (optional)
 * @param {string} props.size - Size of stars ('sm', 'md', 'lg')
 * @param {string} props.className - Additional CSS classes
 * @returns {JSX.Element} Star display component
 */
export function StarDisplay({ rating, count, size = 'sm', className = '' }) {
  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-5 w-5',
    lg: 'h-6 w-6',
  };

  return (
    <div className={`flex items-center space-x-1 ${className}`}>
      <div className="flex items-center">
        {[1, 2, 3, 4, 5].map((star) => {
          const isFilled = star <= rating;
          const isPartial = star - 0.5 <= rating && star > rating;

          return (
            <div key={star} className="relative">
              {isFilled ? (
                <StarIcon className={`${sizeClasses[size]} text-yellow-400`} />
              ) : isPartial ? (
                <>
                  <StarOutlineIcon className={`${sizeClasses[size]} text-gray-300`} />
                  <div
                    className="absolute inset-0 overflow-hidden"
                    style={{ width: '50%' }}
                  >
                    <StarIcon className={`${sizeClasses[size]} text-yellow-400`} />
                  </div>
                </>
              ) : (
                <StarOutlineIcon className={`${sizeClasses[size]} text-gray-300`} />
              )}
            </div>
          );
        })}
      </div>

      <span className="text-sm text-gray-600">
        {rating.toFixed(1)}
      </span>

      {count !== undefined && (
        <span className="text-sm text-gray-500">
          ({count} review{count !== 1 ? 's' : ''})
        </span>
      )}
    </div>
  );
}
