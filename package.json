{"name": "luyaridesignstemplate", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "seed": "node scripts/seed.js"}, "dependencies": {"@auth/mongodb-adapter": "^3.9.1", "@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "@react-three/drei": "^10.0.8", "@react-three/fiber": "^9.1.2", "@stripe/stripe-js": "^7.3.1", "@types/bcryptjs": "^2.4.6", "@types/nodemailer": "^6.4.17", "@types/three": "^0.176.0", "@use-gesture/react": "^10.3.1", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "firebase": "^11.8.1", "formidable": "^3.5.4", "lucide-react": "^0.511.0", "mongodb": "^6.16.0", "mongoose": "^8.15.1", "next": "^15.3.2", "next-auth": "^5.0.0-beta.28", "nodemailer": "^6.10.1", "react": "^19.1.0", "react-dom": "^19.1.0", "react-icons": "^5.5.0", "stripe": "^18.1.1", "three": "^0.176.0", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "eslint": "^9", "eslint-config-next": "15.3.2", "tailwindcss": "^4"}}