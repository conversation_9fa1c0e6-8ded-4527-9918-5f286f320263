import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import connectDB from '@/lib/mongoose';
import Review from '@/models/Review';
import Product from '@/models/Product';
import User from '@/models/User';
import Order from '@/models/Order';

export async function GET(request) {
  try {
    await connectDB();
    
    const { searchParams } = new URL(request.url);
    const productId = searchParams.get('productId');
    const userId = searchParams.get('userId');
    const status = searchParams.get('status') || 'approved';
    const sortBy = searchParams.get('sortBy') || 'newest';
    const page = parseInt(searchParams.get('page')) || 1;
    const limit = parseInt(searchParams.get('limit')) || 10;

    // Build query
    let query = { status };
    
    if (productId) {
      query.product = productId;
    }
    
    if (userId) {
      query.user = userId;
    }

    // Build sort options
    let sortOptions = {};
    switch (sortBy) {
      case 'newest':
        sortOptions = { createdAt: -1 };
        break;
      case 'oldest':
        sortOptions = { createdAt: 1 };
        break;
      case 'highest':
        sortOptions = { rating: -1, createdAt: -1 };
        break;
      case 'lowest':
        sortOptions = { rating: 1, createdAt: -1 };
        break;
      case 'helpful':
        // This would require aggregation for helpful score
        sortOptions = { createdAt: -1 };
        break;
      default:
        sortOptions = { createdAt: -1 };
    }

    // Execute query with pagination
    const skip = (page - 1) * limit;
    const reviews = await Review.find(query)
      .populate('user', 'name image')
      .populate('product', 'title')
      .sort(sortOptions)
      .skip(skip)
      .limit(limit)
      .lean();

    const total = await Review.countDocuments(query);
    
    // Calculate helpful scores for each review
    const reviewsWithScores = reviews.map(review => ({
      ...review,
      helpfulScore: review.helpful ? 
        review.helpful.filter(h => h.helpful).length - 
        review.helpful.filter(h => !h.helpful).length : 0,
    }));

    return NextResponse.json({
      reviews: reviewsWithScores,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error('Error fetching reviews:', error);
    return NextResponse.json(
      { error: 'Failed to fetch reviews' },
      { status: 500 }
    );
  }
}

export async function POST(request) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    await connectDB();
    
    const { productId, rating, title, comment, images } = await request.json();
    
    // Validate required fields
    if (!productId || !rating || !title || !comment) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Check if product exists
    const product = await Product.findById(productId);
    if (!product) {
      return NextResponse.json(
        { error: 'Product not found' },
        { status: 404 }
      );
    }

    // Check if user already reviewed this product
    const existingReview = await Review.findOne({
      user: session.user.id,
      product: productId,
    });

    if (existingReview) {
      return NextResponse.json(
        { error: 'You have already reviewed this product' },
        { status: 400 }
      );
    }

    // Check if user purchased this product (for verified reviews)
    const hasPurchased = await Order.findOne({
      user: session.user.id,
      'items.product': productId,
      status: 'completed',
    });

    // Create review
    const review = new Review({
      user: session.user.id,
      product: productId,
      rating,
      title,
      comment,
      images: images || [],
      verified: !!hasPurchased,
      status: 'pending', // Reviews need moderation
    });

    await review.save();
    
    // Populate user data for response
    await review.populate('user', 'name image');
    
    return NextResponse.json(review, { status: 201 });
  } catch (error) {
    console.error('Error creating review:', error);
    return NextResponse.json(
      { error: 'Failed to create review' },
      { status: 500 }
    );
  }
}
