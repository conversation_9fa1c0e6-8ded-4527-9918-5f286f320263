/**
 * Google Analytics 4 configuration and tracking functions
 */

// Google Analytics 4 configuration
export const GA_TRACKING_ID = process.env.NEXT_PUBLIC_GA_ID;

/**
 * Initialize Google Analytics
 */
export const initGA = () => {
  if (typeof window !== 'undefined' && GA_TRACKING_ID) {
    // Load gtag script
    const script = document.createElement('script');
    script.async = true;
    script.src = `https://www.googletagmanager.com/gtag/js?id=${GA_TRACKING_ID}`;
    document.head.appendChild(script);

    // Initialize gtag
    window.dataLayer = window.dataLayer || [];
    function gtag() {
      window.dataLayer.push(arguments);
    }
    window.gtag = gtag;

    gtag('js', new Date());
    gtag('config', GA_TRACKING_ID, {
      page_title: document.title,
      page_location: window.location.href,
    });
  }
};

/**
 * Track page views
 * @param {string} url - The page URL to track
 */
export const trackPageView = (url) => {
  if (typeof window !== 'undefined' && window.gtag && GA_TRACKING_ID) {
    window.gtag('config', GA_TRACKING_ID, {
      page_path: url,
    });
  }
};

// Track custom events
export const trackEvent = (action, category, label, value) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', action, {
      event_category: category,
      event_label: label,
      value: value,
    });
  }
};

// E-commerce tracking events
export const trackPurchase = (transactionId, items, value, currency = 'USD') => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', 'purchase', {
      transaction_id: transactionId,
      value: value,
      currency: currency,
      items: items.map(item => ({
        item_id: item.id,
        item_name: item.title,
        category: item.category,
        quantity: item.quantity,
        price: item.price,
      })),
    });
  }
};

export const trackAddToCart = (item) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', 'add_to_cart', {
      currency: 'USD',
      value: item.salePrice || item.price,
      items: [{
        item_id: item.id,
        item_name: item.title,
        category: item.category,
        quantity: 1,
        price: item.salePrice || item.price,
      }],
    });
  }
};

export const trackRemoveFromCart = (item) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', 'remove_from_cart', {
      currency: 'USD',
      value: item.salePrice || item.price,
      items: [{
        item_id: item.id,
        item_name: item.title,
        category: item.category,
        quantity: 1,
        price: item.salePrice || item.price,
      }],
    });
  }
};

export const trackViewItem = (item) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', 'view_item', {
      currency: 'USD',
      value: item.salePrice || item.price,
      items: [{
        item_id: item.id,
        item_name: item.title,
        category: item.category,
        price: item.salePrice || item.price,
      }],
    });
  }
};

export const trackBeginCheckout = (items, value) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', 'begin_checkout', {
      currency: 'USD',
      value: value,
      items: items.map(item => ({
        item_id: item.id,
        item_name: item.title,
        category: item.category,
        quantity: item.quantity,
        price: item.salePrice || item.price,
      })),
    });
  }
};

export const trackSearch = (searchTerm, category) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', 'search', {
      search_term: searchTerm,
      category: category,
    });
  }
};

export const trackAddToWishlist = (item) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', 'add_to_wishlist', {
      currency: 'USD',
      value: item.salePrice || item.price,
      items: [{
        item_id: item.id,
        item_name: item.title,
        category: item.category,
        price: item.salePrice || item.price,
      }],
    });
  }
};

export const trackShare = (contentType, itemId, method) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', 'share', {
      content_type: contentType,
      item_id: itemId,
      method: method,
    });
  }
};

export const trackSignUp = (method) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', 'sign_up', {
      method: method,
    });
  }
};

export const trackLogin = (method) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', 'login', {
      method: method,
    });
  }
};

/**
 * Set user context for analytics
 * @param {Object} user - User object with id, email, name
 */
export const setUserContext = (user) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('config', GA_TRACKING_ID, {
      user_id: user.id,
      custom_map: {
        user_email: user.email,
        user_name: user.name,
        user_role: user.role,
      },
    });
  }
};
