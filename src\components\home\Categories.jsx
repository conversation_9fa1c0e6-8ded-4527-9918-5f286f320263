'use client';

import Link from 'next/link';
import { 
  HomeIcon, 
  PaintBrushIcon, 
  DevicePhoneMobileIcon,
  PhotoIcon,
  DocumentTextIcon,
  CubeIcon 
} from '@heroicons/react/24/outline';

export default function Categories() {
  const categories = [
    {
      name: 'House Plans',
      description: 'Modern and traditional house designs',
      href: '/collections/house-plans',
      icon: HomeIcon,
      color: 'bg-blue-500',
      count: '2,500+',
    },
    {
      name: 'UI Kits',
      description: 'Complete interface design systems',
      href: '/collections/ui-kits',
      icon: DevicePhoneMobileIcon,
      color: 'bg-purple-500',
      count: '800+',
    },
    {
      name: 'Templates',
      description: 'Ready-to-use design templates',
      href: '/collections/templates',
      icon: DocumentTextIcon,
      color: 'bg-green-500',
      count: '1,200+',
    },
    {
      name: 'Graphics',
      description: 'Icons, illustrations, and graphics',
      href: '/collections/graphics',
      icon: PaintBrushIcon,
      color: 'bg-orange-500',
      count: '3,000+',
    },
    {
      name: 'Mockups',
      description: 'Professional presentation mockups',
      href: '/collections/mockups',
      icon: PhotoIcon,
      color: 'bg-pink-500',
      count: '600+',
    },
    {
      name: '3D Models',
      description: 'High-quality 3D assets',
      href: '/collections/3d-models',
      icon: CubeIcon,
      color: 'bg-indigo-500',
      count: '400+',
    },
  ];

  return (
    <section className="py-16 bg-gray-50">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">Browse by Category</h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Find exactly what you need from our carefully curated categories
          </p>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
          {categories.map((category) => {
            const IconComponent = category.icon;
            return (
              <Link
                key={category.name}
                href={category.href}
                className="group relative bg-white rounded-xl p-6 shadow-sm border border-gray-200 hover:shadow-lg hover:border-gray-300 transition-all duration-300"
              >
                <div className="flex items-start space-x-4">
                  <div className={`${category.color} p-3 rounded-lg group-hover:scale-110 transition-transform`}>
                    <IconComponent className="h-6 w-6 text-white" />
                  </div>
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">
                      {category.name}
                    </h3>
                    <p className="text-sm text-gray-600 mt-1">
                      {category.description}
                    </p>
                    <div className="mt-3 flex items-center justify-between">
                      <span className="text-sm font-medium text-gray-500">
                        {category.count} items
                      </span>
                      <span className="text-blue-600 text-sm font-medium group-hover:translate-x-1 transition-transform">
                        Browse →
                      </span>
                    </div>
                  </div>
                </div>
              </Link>
            );
          })}
        </div>

        {/* Featured Category Banner */}
        <div className="mt-16 bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl p-8 text-center text-white">
          <h3 className="text-2xl font-bold mb-4">House Plans Collection</h3>
          <p className="text-lg opacity-90 mb-6 max-w-2xl mx-auto">
            Discover our extensive collection of modern house plans, from cozy bungalows to luxury mansions. 
            All plans include detailed blueprints and construction documents.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/collections/house-plans"
              className="inline-flex items-center justify-center px-6 py-3 bg-white text-blue-600 font-medium rounded-lg hover:bg-gray-100 transition-colors"
            >
              Browse House Plans
            </Link>
            <Link
              href="/custom-plan"
              className="inline-flex items-center justify-center px-6 py-3 border border-white text-white font-medium rounded-lg hover:bg-white hover:text-blue-600 transition-colors"
            >
              Custom Design
            </Link>
          </div>
        </div>
      </div>
    </section>
  );
}
