"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("@babel/runtime/helpers/extends"),r=require("three"),t=require("react");function n(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function u(e){if(e&&e.__esModule)return e;var r=Object.create(null);return e&&Object.keys(e).forEach((function(t){if("default"!==t){var n=Object.getOwnPropertyDescriptor(e,t);Object.defineProperty(r,t,n.get?n:{enumerable:!0,get:function(){return e[t]}})}})),r.default=e,Object.freeze(r)}var o=n(e),f=u(r),c=u(t);function i(){const e=new f.BufferGeometry,r=new Float32Array([-1,-1,3,-1,-1,3]);return e.boundingSphere=new f.Sphere,e.boundingSphere.set(new f.Vector3,1/0),e.setAttribute("position",new f.BufferAttribute(r,2)),e}const a=c.forwardRef((function({children:e,...r},t){const n=c.useMemo(i,[]);return c.createElement("mesh",o.default({ref:t,geometry:n,frustumCulled:!1},r),e)}));exports.ScreenQuad=a;
