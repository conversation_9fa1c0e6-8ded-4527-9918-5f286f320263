"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("@babel/runtime/helpers/extends"),t=require("react"),r=require("three"),n=require("three-stdlib");function s(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function a(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var n=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,n.get?n:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,Object.freeze(t)}var u=s(e),c=a(t);const o=1e-5;const l=c.forwardRef((function({args:[e=1,t=1,s=1]=[],radius:a=.05,steps:l=1,smoothness:f=4,bevelSegments:i=4,creaseAngle:b=.4,children:d,...h},m){const p=c.useMemo((()=>function(e,t,n){const s=new r.Shape,a=n-o;return s.absarc(o,o,o,-Math.PI/2,-Math.PI,!0),s.absarc(o,t-2*a,o,Math.PI,Math.PI/2,!0),s.absarc(e-2*a,t-2*a,o,Math.PI/2,0,!0),s.absarc(e-2*a,o,o,0,-Math.PI/2,!0),s}(e,t,a)),[e,t,a]),v=c.useMemo((()=>({depth:s-2*a,bevelEnabled:!0,bevelSegments:2*i,steps:l,bevelSize:a-o,bevelThickness:a,curveSegments:f})),[s,a,f]),M=c.useRef(null);return c.useLayoutEffect((()=>{M.current&&(M.current.center(),n.toCreasedNormals(M.current,b))}),[p,v]),c.createElement("mesh",u.default({ref:m},h),c.createElement("extrudeGeometry",{ref:M,args:[p,v]}),d)}));exports.RoundedBox=l;
