import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import connectDB from '@/lib/mongoose';
import Wishlist from '@/models/Wishlist';
import User from '@/models/User';

export async function GET(request) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    await connectDB();
    
    // Get or create user's wishlist
    let wishlist = await Wishlist.findOne({ user: session.user.id })
      .populate({
        path: 'items.product',
        select: 'title price salePrice images category status',
      });

    if (!wishlist) {
      wishlist = new Wishlist({
        user: session.user.id,
        name: 'My Wishlist',
        items: [],
      });
      await wishlist.save();
      
      // Update user's wishlist reference
      await User.findByIdAndUpdate(session.user.id, { wishlist: wishlist._id });
    }
    
    return NextResponse.json(wishlist);
  } catch (error) {
    console.error('Error fetching wishlist:', error);
    return NextResponse.json(
      { error: 'Failed to fetch wishlist' },
      { status: 500 }
    );
  }
}

export async function POST(request) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    await connectDB();
    
    const { productId, notes } = await request.json();
    
    if (!productId) {
      return NextResponse.json(
        { error: 'Product ID is required' },
        { status: 400 }
      );
    }

    // Get or create user's wishlist
    let wishlist = await Wishlist.findOne({ user: session.user.id });

    if (!wishlist) {
      wishlist = new Wishlist({
        user: session.user.id,
        name: 'My Wishlist',
        items: [],
      });
      
      // Update user's wishlist reference
      await User.findByIdAndUpdate(session.user.id, { wishlist: wishlist._id });
    }

    // Check if product is already in wishlist
    const existingItem = wishlist.items.find(
      item => item.product.toString() === productId
    );

    if (existingItem) {
      return NextResponse.json(
        { error: 'Product already in wishlist' },
        { status: 400 }
      );
    }

    // Add product to wishlist
    await wishlist.addItem(productId, notes);
    
    // Populate product data for response
    await wishlist.populate({
      path: 'items.product',
      select: 'title price salePrice images category status',
    });
    
    return NextResponse.json(wishlist);
  } catch (error) {
    console.error('Error adding to wishlist:', error);
    return NextResponse.json(
      { error: 'Failed to add to wishlist' },
      { status: 500 }
    );
  }
}

export async function PUT(request) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    await connectDB();
    
    const { name, description, isPublic, emailNotifications } = await request.json();
    
    const wishlist = await Wishlist.findOne({ user: session.user.id });
    
    if (!wishlist) {
      return NextResponse.json(
        { error: 'Wishlist not found' },
        { status: 404 }
      );
    }

    // Update wishlist properties
    if (name !== undefined) wishlist.name = name;
    if (description !== undefined) wishlist.description = description;
    if (isPublic !== undefined) wishlist.isPublic = isPublic;
    if (emailNotifications !== undefined) {
      wishlist.emailNotifications = { ...wishlist.emailNotifications, ...emailNotifications };
    }

    await wishlist.save();
    
    return NextResponse.json(wishlist);
  } catch (error) {
    console.error('Error updating wishlist:', error);
    return NextResponse.json(
      { error: 'Failed to update wishlist' },
      { status: 500 }
    );
  }
}
