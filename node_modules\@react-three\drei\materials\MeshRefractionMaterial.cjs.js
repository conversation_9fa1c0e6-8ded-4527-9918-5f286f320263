"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("three"),r=require("../core/shaderMaterial.cjs.js"),n=require("three-mesh-bvh"),i=require("../helpers/constants.cjs.js");function o(e){if(e&&e.__esModule)return e;var r=Object.create(null);return e&&Object.keys(e).forEach((function(n){if("default"!==n){var i=Object.getOwnPropertyDescriptor(e,n);Object.defineProperty(r,n,i.get?i:{enumerable:!0,get:function(){return e[n]}})}})),r.default=e,Object.freeze(r)}var t=o(e);const a=r.shaderMaterial({envMap:null,bounces:3,ior:2.4,correctMips:!0,aberrationStrength:.01,fresnel:0,bvh:new n.MeshBVHUniformStruct,color:new t.Color("white"),opacity:1,resolution:new t.Vector2,viewMatrixInverse:new t.Matrix4,projectionMatrixInverse:new t.Matrix4},"\n  uniform mat4 viewMatrixInverse;\n\n  varying vec3 vWorldPosition;\n  varying vec3 vNormal;\n  varying mat4 vModelMatrixInverse;\n\n  #include <color_pars_vertex>\n\n  void main() {\n    #include <color_vertex>\n\n    vec4 transformedNormal = vec4(normal, 0.0);\n    vec4 transformedPosition = vec4(position, 1.0);\n    #ifdef USE_INSTANCING\n      transformedNormal = instanceMatrix * transformedNormal;\n      transformedPosition = instanceMatrix * transformedPosition;\n    #endif\n\n    #ifdef USE_INSTANCING\n      vModelMatrixInverse = inverse(modelMatrix * instanceMatrix);\n    #else\n      vModelMatrixInverse = inverse(modelMatrix);\n    #endif\n\n    vWorldPosition = (modelMatrix * transformedPosition).xyz;\n    vNormal = normalize((viewMatrixInverse * vec4(normalMatrix * transformedNormal.xyz, 0.0)).xyz);\n    gl_Position = projectionMatrix * viewMatrix * modelMatrix * transformedPosition;\n  }",`\n  #define ENVMAP_TYPE_CUBE_UV\n  precision highp isampler2D;\n  precision highp usampler2D;\n  varying vec3 vWorldPosition;\n  varying vec3 vNormal;\n  varying mat4 vModelMatrixInverse;\n\n  #include <color_pars_fragment>\n\n  #ifdef ENVMAP_TYPE_CUBEM\n    uniform samplerCube envMap;\n  #else\n    uniform sampler2D envMap;\n  #endif\n\n  uniform float bounces;\n  ${n.shaderStructs}\n  ${n.shaderIntersectFunction}\n  uniform BVH bvh;\n  uniform float ior;\n  uniform bool correctMips;\n  uniform vec2 resolution;\n  uniform float fresnel;\n  uniform mat4 modelMatrix;\n  uniform mat4 projectionMatrixInverse;\n  uniform mat4 viewMatrixInverse;\n  uniform float aberrationStrength;\n  uniform vec3 color;\n  uniform float opacity;\n\n  float fresnelFunc(vec3 viewDirection, vec3 worldNormal) {\n    return pow( 1.0 + dot( viewDirection, worldNormal), 10.0 );\n  }\n\n  vec3 totalInternalReflection(vec3 ro, vec3 rd, vec3 normal, float ior, mat4 modelMatrixInverse) {\n    vec3 rayOrigin = ro;\n    vec3 rayDirection = rd;\n    rayDirection = refract(rayDirection, normal, 1.0 / ior);\n    rayOrigin = vWorldPosition + rayDirection * 0.001;\n    rayOrigin = (modelMatrixInverse * vec4(rayOrigin, 1.0)).xyz;\n    rayDirection = normalize((modelMatrixInverse * vec4(rayDirection, 0.0)).xyz);\n    for(float i = 0.0; i < bounces; i++) {\n      uvec4 faceIndices = uvec4( 0u );\n      vec3 faceNormal = vec3( 0.0, 0.0, 1.0 );\n      vec3 barycoord = vec3( 0.0 );\n      float side = 1.0;\n      float dist = 0.0;\n      bvhIntersectFirstHit( bvh, rayOrigin, rayDirection, faceIndices, faceNormal, barycoord, side, dist );\n      vec3 hitPos = rayOrigin + rayDirection * max(dist - 0.001, 0.0);\n      vec3 tempDir = refract(rayDirection, faceNormal, ior);\n      if (length(tempDir) != 0.0) {\n        rayDirection = tempDir;\n        break;\n      }\n      rayDirection = reflect(rayDirection, faceNormal);\n      rayOrigin = hitPos + rayDirection * 0.01;\n    }\n    rayDirection = normalize((modelMatrix * vec4(rayDirection, 0.0)).xyz);\n    return rayDirection;\n  }\n\n  #include <common>\n  #include <cube_uv_reflection_fragment>\n\n  #ifdef ENVMAP_TYPE_CUBEM\n    vec4 textureGradient(samplerCube envMap, vec3 rayDirection, vec3 directionCamPerfect) {\n      return textureGrad(envMap, rayDirection, dFdx(correctMips ? directionCamPerfect: rayDirection), dFdy(correctMips ? directionCamPerfect: rayDirection));\n    }\n  #else\n    vec4 textureGradient(sampler2D envMap, vec3 rayDirection, vec3 directionCamPerfect) {\n      vec2 uvv = equirectUv( rayDirection );\n      vec2 smoothUv = equirectUv( directionCamPerfect );\n      return textureGrad(envMap, uvv, dFdx(correctMips ? smoothUv : uvv), dFdy(correctMips ? smoothUv : uvv));\n    }\n  #endif\n\n  void main() {\n    vec2 uv = gl_FragCoord.xy / resolution;\n    vec3 directionCamPerfect = (projectionMatrixInverse * vec4(uv * 2.0 - 1.0, 0.0, 1.0)).xyz;\n    directionCamPerfect = (viewMatrixInverse * vec4(directionCamPerfect, 0.0)).xyz;\n    directionCamPerfect = normalize(directionCamPerfect);\n    vec3 normal = vNormal;\n    vec3 rayOrigin = cameraPosition;\n    vec3 rayDirection = normalize(vWorldPosition - cameraPosition);\n\n    vec4 diffuseColor = vec4(color, opacity);\n    #include <color_fragment>\n\n    #ifdef CHROMATIC_ABERRATIONS\n      vec3 rayDirectionG = totalInternalReflection(rayOrigin, rayDirection, normal, max(ior, 1.0), vModelMatrixInverse);\n      #ifdef FAST_CHROMA\n        vec3 rayDirectionR = normalize(rayDirectionG + 1.0 * vec3(aberrationStrength / 2.0));\n        vec3 rayDirectionB = normalize(rayDirectionG - 1.0 * vec3(aberrationStrength / 2.0));\n      #else\n        vec3 rayDirectionR = totalInternalReflection(rayOrigin, rayDirection, normal, max(ior * (1.0 - aberrationStrength), 1.0), vModelMatrixInverse);\n        vec3 rayDirectionB = totalInternalReflection(rayOrigin, rayDirection, normal, max(ior * (1.0 + aberrationStrength), 1.0), vModelMatrixInverse);\n      #endif\n      float finalColorR = textureGradient(envMap, rayDirectionR, directionCamPerfect).r;\n      float finalColorG = textureGradient(envMap, rayDirectionG, directionCamPerfect).g;\n      float finalColorB = textureGradient(envMap, rayDirectionB, directionCamPerfect).b;\n      diffuseColor.rgb *= vec3(finalColorR, finalColorG, finalColorB);\n    #else\n      rayDirection = totalInternalReflection(rayOrigin, rayDirection, normal, max(ior, 1.0), vModelMatrixInverse);\n      diffuseColor.rgb *= textureGradient(envMap, rayDirection, directionCamPerfect).rgb;\n    #endif\n\n    vec3 viewDirection = normalize(vWorldPosition - cameraPosition);\n    float nFresnel = fresnelFunc(viewDirection, normal) * fresnel;\n    gl_FragColor = vec4(mix(diffuseColor.rgb, vec3(1.0), nFresnel), diffuseColor.a);\n\n    #include <tonemapping_fragment>\n    #include <${i.version>=154?"colorspace_fragment":"encodings_fragment"}>\n  }`);exports.MeshRefractionMaterial=a;
