"use strict";function e(e){if(e&&e.__esModule)return e;var r=Object.create(null);return e&&Object.keys(e).forEach((function(t){if("default"!==t){var n=Object.getOwnPropertyDescriptor(e,t);Object.defineProperty(r,t,n.get?n:{enumerable:!0,get:function(){return e[t]}})}})),r.default=e,Object.freeze(r)}Object.defineProperty(exports,"__esModule",{value:!0});var r=e(require("three"));exports.shaderMaterial=function(e,t,n,i){var s;return(s=class extends r.ShaderMaterial{constructor(s){super({vertexShader:t,fragmentShader:n,...s});for(const t in e)this.uniforms[t]=new r.Uniform(e[t]),Object.defineProperty(this,t,{get(){return this.uniforms[t].value},set(e){this.uniforms[t].value=e}});this.uniforms=r.UniformsUtils.clone(this.uniforms),null==i||i(this)}}).key=r.MathUtils.generateUUID(),s};
