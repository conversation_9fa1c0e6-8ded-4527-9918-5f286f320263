'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import { useCart } from '@/components/providers/CartProvider';
import { toast } from '@/components/ui/Toaster';
import WishlistButton from '@/components/wishlist/WishlistButton';
import { StarDisplay } from '@/components/ui/StarRating';
import { 
  TrashIcon, 
  ShoppingCartIcon, 
  ShareIcon,
  HeartIcon,
  Cog6ToothIcon 
} from '@heroicons/react/24/outline';

export default function WishlistPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const { addItem } = useCart();
  const [wishlist, setWishlist] = useState(null);
  const [loading, setLoading] = useState(true);
  const [showSettings, setShowSettings] = useState(false);

  useEffect(() => {
    if (status === 'loading') return;
    
    if (!session) {
      router.push('/auth/signin?callbackUrl=/wishlist');
      return;
    }

    fetchWishlist();
  }, [session, status, router]);

  const fetchWishlist = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/wishlist');
      
      if (response.ok) {
        const data = await response.json();
        setWishlist(data);
      } else {
        toast.error('Failed to load wishlist');
      }
    } catch (error) {
      console.error('Error fetching wishlist:', error);
      toast.error('Something went wrong');
    } finally {
      setLoading(false);
    }
  };

  const handleAddToCart = (product) => {
    addItem({
      id: product._id,
      title: product.title,
      price: product.price,
      salePrice: product.salePrice,
      image: product.images?.[0]?.url,
    });
    toast.success('Added to cart!');
  };

  const handleRemoveItem = async (productId) => {
    try {
      const response = await fetch(`/api/wishlist/${productId}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        const updatedWishlist = await response.json();
        setWishlist(updatedWishlist);
        toast.success('Removed from wishlist');
      } else {
        toast.error('Failed to remove item');
      }
    } catch (error) {
      console.error('Error removing item:', error);
      toast.error('Something went wrong');
    }
  };

  const handleShareWishlist = async () => {
    try {
      const response = await fetch('/api/wishlist', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ isPublic: true }),
      });

      if (response.ok) {
        const updatedWishlist = await response.json();
        setWishlist(updatedWishlist);
        
        const shareUrl = `${window.location.origin}/wishlist/shared/${updatedWishlist.shareToken}`;
        await navigator.clipboard.writeText(shareUrl);
        toast.success('Share link copied to clipboard!');
      } else {
        toast.error('Failed to create share link');
      }
    } catch (error) {
      console.error('Error sharing wishlist:', error);
      toast.error('Something went wrong');
    }
  };

  if (status === 'loading' || loading) {
    return (
      <div className="min-h-screen bg-white">
        <Header />
        <main className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-16">
          <div className="animate-pulse space-y-4">
            <div className="h-8 bg-gray-200 rounded w-1/4"></div>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
              {[...Array(6)].map((_, i) => (
                <div key={i} className="bg-gray-200 h-64 rounded-lg"></div>
              ))}
            </div>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  if (!session) {
    return null; // Will redirect
  }

  const isEmpty = !wishlist || wishlist.items.length === 0;

  return (
    <div className="min-h-screen bg-white">
      <Header />
      
      <main className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">My Wishlist</h1>
            {wishlist && (
              <p className="text-gray-600 mt-1">
                {wishlist.itemCount} item{wishlist.itemCount !== 1 ? 's' : ''}
              </p>
            )}
          </div>
          
          {!isEmpty && (
            <div className="flex items-center space-x-3">
              <button
                onClick={handleShareWishlist}
                className="flex items-center space-x-2 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <ShareIcon className="h-4 w-4" />
                <span>Share</span>
              </button>
              
              <button
                onClick={() => setShowSettings(!showSettings)}
                className="flex items-center space-x-2 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <Cog6ToothIcon className="h-4 w-4" />
                <span>Settings</span>
              </button>
            </div>
          )}
        </div>

        {/* Settings Panel */}
        {showSettings && (
          <div className="bg-gray-50 rounded-lg p-6 mb-8">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Wishlist Settings</h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Wishlist Name
                </label>
                <input
                  type="text"
                  value={wishlist?.name || ''}
                  onChange={(e) => {
                    // Update wishlist name
                  }}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="emailNotifications"
                  checked={wishlist?.emailNotifications?.priceDrops || false}
                  onChange={(e) => {
                    // Update email notifications
                  }}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <label htmlFor="emailNotifications" className="text-sm text-gray-700">
                  Email me when items go on sale
                </label>
              </div>
            </div>
          </div>
        )}

        {/* Empty State */}
        {isEmpty ? (
          <div className="text-center py-16">
            <HeartIcon className="mx-auto h-24 w-24 text-gray-300 mb-4" />
            <h2 className="text-2xl font-semibold text-gray-900 mb-2">Your wishlist is empty</h2>
            <p className="text-gray-600 mb-8 max-w-md mx-auto">
              Save items you love by clicking the heart icon on any product. 
              We'll keep them safe here for you.
            </p>
            <Link
              href="/shop"
              className="inline-flex items-center px-6 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors"
            >
              Start Shopping
            </Link>
          </div>
        ) : (
          /* Wishlist Items */
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {wishlist.items.map((item) => (
              <div key={item._id} className="group relative bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-lg transition-shadow">
                {/* Product Image */}
                <div className="relative overflow-hidden rounded-t-lg">
                  <Link href={`/products/${item.product._id}`}>
                    <img
                      src={item.product.images?.[0]?.url || '/api/placeholder/400/300'}
                      alt={item.product.title}
                      className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                    />
                  </Link>
                  
                  {/* Remove Button */}
                  <button
                    onClick={() => handleRemoveItem(item.product._id)}
                    className="absolute top-2 right-2 p-2 bg-white rounded-full shadow-sm hover:shadow-md transition-shadow"
                    title="Remove from wishlist"
                  >
                    <TrashIcon className="h-4 w-4 text-gray-600 hover:text-red-600" />
                  </button>
                  
                  {/* Sale Badge */}
                  {item.product.salePrice && (
                    <span className="absolute top-2 left-2 bg-green-500 text-white text-xs px-2 py-1 rounded">
                      Sale
                    </span>
                  )}
                </div>

                {/* Product Info */}
                <div className="p-4">
                  <Link href={`/products/${item.product._id}`}>
                    <h3 className="font-semibold text-gray-900 mb-2 line-clamp-2 hover:text-blue-600 transition-colors">
                      {item.product.title}
                    </h3>
                  </Link>
                  
                  {/* Rating */}
                  {item.product.rating && item.product.rating.count > 0 && (
                    <div className="mb-2">
                      <StarDisplay 
                        rating={item.product.rating.average} 
                        count={item.product.rating.count}
                        size="sm" 
                      />
                    </div>
                  )}
                  
                  {/* Price */}
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-2">
                      {item.product.salePrice ? (
                        <>
                          <span className="text-lg font-bold text-gray-900">
                            ${item.product.salePrice}
                          </span>
                          <span className="text-sm text-gray-500 line-through">
                            ${item.product.price}
                          </span>
                        </>
                      ) : (
                        <span className="text-lg font-bold text-gray-900">
                          ${item.product.price}
                        </span>
                      )}
                    </div>
                  </div>
                  
                  {/* Added Date */}
                  <p className="text-xs text-gray-500 mb-3">
                    Added {new Date(item.addedAt).toLocaleDateString()}
                  </p>
                  
                  {/* Actions */}
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => handleAddToCart(item.product)}
                      className="flex-1 bg-blue-600 text-white px-3 py-2 rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium flex items-center justify-center space-x-1"
                    >
                      <ShoppingCartIcon className="h-4 w-4" />
                      <span>Add to Cart</span>
                    </button>
                    
                    <WishlistButton
                      productId={item.product._id}
                      size="md"
                      className="p-2 border border-gray-300 rounded-lg hover:bg-gray-50"
                      onToggle={(inWishlist) => {
                        if (!inWishlist) {
                          fetchWishlist(); // Refresh the list
                        }
                      }}
                    />
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </main>

      <Footer />
    </div>
  );
}
