'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { StarDisplay } from '@/components/ui/StarRating';
import { toast } from '@/components/ui/Toaster';
import { 
  HandThumbUpIcon, 
  HandThumbDownIcon,
  CheckBadgeIcon,
  ChevronDownIcon 
} from '@heroicons/react/24/outline';
import { 
  HandThumbUpIcon as HandThumbUpSolidIcon, 
  HandThumbDownIcon as HandThumbDownSolidIcon 
} from '@heroicons/react/24/solid';

export default function ReviewList({ productId, onReviewsLoad }) {
  const { data: session } = useSession();
  const [reviews, setReviews] = useState([]);
  const [loading, setLoading] = useState(true);
  const [sortBy, setSortBy] = useState('newest');
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(false);
  const [userVotes, setUserVotes] = useState({});

  useEffect(() => {
    fetchReviews(true);
  }, [productId, sortBy]);

  const fetchReviews = async (reset = false) => {
    try {
      setLoading(true);
      const currentPage = reset ? 1 : page;
      
      const response = await fetch(
        `/api/reviews?productId=${productId}&sortBy=${sortBy}&page=${currentPage}&limit=10`
      );
      
      if (response.ok) {
        const data = await response.json();
        
        if (reset) {
          setReviews(data.reviews);
          setPage(1);
        } else {
          setReviews(prev => [...prev, ...data.reviews]);
        }
        
        setHasMore(data.pagination.page < data.pagination.pages);
        
        if (onReviewsLoad) {
          onReviewsLoad(data.reviews);
        }
        
        // Load user votes if signed in
        if (session && data.reviews.length > 0) {
          loadUserVotes(data.reviews);
        }
      }
    } catch (error) {
      console.error('Error fetching reviews:', error);
      toast.error('Failed to load reviews');
    } finally {
      setLoading(false);
    }
  };

  const loadUserVotes = (reviewList) => {
    const votes = {};
    reviewList.forEach(review => {
      const userVote = review.helpful?.find(h => h.user === session.user.id);
      if (userVote) {
        votes[review._id] = userVote.helpful;
      }
    });
    setUserVotes(votes);
  };

  const handleLoadMore = () => {
    setPage(prev => prev + 1);
    fetchReviews(false);
  };

  const handleHelpfulVote = async (reviewId, helpful) => {
    if (!session) {
      toast.error('Please sign in to vote');
      return;
    }

    try {
      const response = await fetch(`/api/reviews/${reviewId}/helpful`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ helpful }),
      });

      if (response.ok) {
        const data = await response.json();
        
        // Update review in list
        setReviews(prev => prev.map(review => 
          review._id === reviewId 
            ? { ...review, helpfulScore: data.helpfulScore }
            : review
        ));
        
        // Update user vote
        setUserVotes(prev => ({
          ...prev,
          [reviewId]: helpful,
        }));
        
        toast.success('Thank you for your feedback!');
      } else {
        const error = await response.json();
        toast.error(error.error || 'Failed to submit vote');
      }
    } catch (error) {
      console.error('Error voting on review:', error);
      toast.error('Something went wrong');
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  if (loading && reviews.length === 0) {
    return (
      <div className="space-y-4">
        {[...Array(3)].map((_, i) => (
          <div key={i} className="animate-pulse">
            <div className="bg-gray-200 h-4 w-1/4 rounded mb-2"></div>
            <div className="bg-gray-200 h-4 w-full rounded mb-1"></div>
            <div className="bg-gray-200 h-4 w-3/4 rounded"></div>
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Sort Controls */}
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-900">
          Customer Reviews ({reviews.length})
        </h3>
        
        <div className="relative">
          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value)}
            className="appearance-none bg-white border border-gray-300 rounded-lg px-4 py-2 pr-8 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="newest">Newest First</option>
            <option value="oldest">Oldest First</option>
            <option value="highest">Highest Rated</option>
            <option value="lowest">Lowest Rated</option>
            <option value="helpful">Most Helpful</option>
          </select>
          <ChevronDownIcon className="absolute right-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-500 pointer-events-none" />
        </div>
      </div>

      {/* Reviews List */}
      {reviews.length === 0 ? (
        <div className="text-center py-8">
          <p className="text-gray-500">No reviews yet. Be the first to review this product!</p>
        </div>
      ) : (
        <div className="space-y-6">
          {reviews.map((review) => (
            <div key={review._id} className="border-b border-gray-200 pb-6 last:border-b-0">
              {/* Review Header */}
              <div className="flex items-start justify-between mb-3">
                <div className="flex items-center space-x-3">
                  <img
                    src={review.user.image || '/api/placeholder/40/40'}
                    alt={review.user.name}
                    className="w-10 h-10 rounded-full object-cover"
                  />
                  <div>
                    <div className="flex items-center space-x-2">
                      <span className="font-medium text-gray-900">
                        {review.user.name}
                      </span>
                      {review.verified && (
                        <CheckBadgeIcon className="h-4 w-4 text-green-500" title="Verified Purchase" />
                      )}
                    </div>
                    <div className="flex items-center space-x-2">
                      <StarDisplay rating={review.rating} size="sm" />
                      <span className="text-sm text-gray-500">
                        {formatDate(review.createdAt)}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Review Content */}
              <div className="mb-4">
                <h4 className="font-semibold text-gray-900 mb-2">{review.title}</h4>
                <p className="text-gray-700 leading-relaxed">{review.comment}</p>
              </div>

              {/* Review Images */}
              {review.images && review.images.length > 0 && (
                <div className="flex space-x-2 mb-4">
                  {review.images.map((image, index) => (
                    <img
                      key={index}
                      src={image.url}
                      alt={image.alt || 'Review image'}
                      className="w-20 h-20 object-cover rounded-lg"
                    />
                  ))}
                </div>
              )}

              {/* Helpful Votes */}
              <div className="flex items-center space-x-4">
                <span className="text-sm text-gray-600">Was this helpful?</span>
                
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => handleHelpfulVote(review._id, true)}
                    disabled={!session}
                    className={`flex items-center space-x-1 px-3 py-1 rounded-lg text-sm transition-colors ${
                      userVotes[review._id] === true
                        ? 'bg-green-100 text-green-700'
                        : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                    } ${!session ? 'cursor-not-allowed opacity-50' : 'cursor-pointer'}`}
                  >
                    {userVotes[review._id] === true ? (
                      <HandThumbUpSolidIcon className="h-4 w-4" />
                    ) : (
                      <HandThumbUpIcon className="h-4 w-4" />
                    )}
                    <span>Yes</span>
                  </button>
                  
                  <button
                    onClick={() => handleHelpfulVote(review._id, false)}
                    disabled={!session}
                    className={`flex items-center space-x-1 px-3 py-1 rounded-lg text-sm transition-colors ${
                      userVotes[review._id] === false
                        ? 'bg-red-100 text-red-700'
                        : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                    } ${!session ? 'cursor-not-allowed opacity-50' : 'cursor-pointer'}`}
                  >
                    {userVotes[review._id] === false ? (
                      <HandThumbDownSolidIcon className="h-4 w-4" />
                    ) : (
                      <HandThumbDownIcon className="h-4 w-4" />
                    )}
                    <span>No</span>
                  </button>
                </div>
                
                {review.helpfulScore > 0 && (
                  <span className="text-sm text-gray-500">
                    {review.helpfulScore} people found this helpful
                  </span>
                )}
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Load More Button */}
      {hasMore && (
        <div className="text-center">
          <button
            onClick={handleLoadMore}
            disabled={loading}
            className="bg-gray-100 text-gray-700 px-6 py-2 rounded-lg hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {loading ? 'Loading...' : 'Load More Reviews'}
          </button>
        </div>
      )}
    </div>
  );
}
