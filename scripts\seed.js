const { MongoClient } = require('mongodb');
require('dotenv').config({ path: '.env.local' });

const uri = process.env.MONGODB_URI || 'mongodb://localhost:27017/luyari-designs';

const sampleProducts = [
  {
    title: 'Modern 4-Bedroom Family Home',
    description: 'A contemporary design featuring an open floor plan, large windows, and sustainable materials. Perfect for modern families who value both style and functionality.',
    shortDescription: 'Contemporary design with open floor plan and large windows',
    price: 299,
    salePrice: 199,
    category: 'house-plans',
    subcategory: 'modern',
    tags: ['modern', 'family', '4-bedroom', 'open-plan', 'sustainable'],
    images: [
      { url: '/api/placeholder/800/600', alt: 'Modern House Plan - Front View', isPrimary: true },
      { url: '/api/placeholder/800/600', alt: 'Modern House Plan - Floor Plan', isPrimary: false },
      { url: '/api/placeholder/800/600', alt: 'Modern House Plan - Interior', isPrimary: false },
    ],
    files: [
      { name: 'Floor Plans.pdf', url: '/files/modern-4br-floor-plans.pdf', type: 'pdf', size: 2048000 },
      { name: 'Elevations.dwg', url: '/files/modern-4br-elevations.dwg', type: 'dwg', size: 1024000 },
      { name: 'Construction Details.pdf', url: '/files/modern-4br-details.pdf', type: 'pdf', size: 3072000 },
    ],
    specifications: {
      dimensions: { width: 15, height: 12, depth: 20, unit: 'm' },
      area: { value: 250, unit: 'sqm' },
      bedrooms: 4,
      bathrooms: 3,
      floors: 2,
      style: 'Modern',
      features: ['Open Floor Plan', 'Master Suite', 'Double Garage', 'Large Kitchen', 'Energy Efficient'],
    },
    status: 'published',
    featured: true,
    bestseller: true,
    downloadCount: 156,
    rating: { average: 4.8, count: 23 },
    seo: {
      metaTitle: 'Modern 4-Bedroom House Plan - Contemporary Family Home Design',
      metaDescription: 'Download our popular modern 4-bedroom house plan featuring open floor plan and sustainable design.',
      slug: 'modern-4-bedroom-family-home',
    },
  },
  {
    title: 'Luxury Villa with Pool',
    description: 'An elegant 5-bedroom luxury villa design featuring a swimming pool, wine cellar, and home theater. This sophisticated design combines luxury with comfort.',
    shortDescription: '5-bedroom luxury villa with swimming pool and premium amenities',
    price: 599,
    salePrice: 449,
    category: 'house-plans',
    subcategory: 'luxury',
    tags: ['luxury', 'villa', '5-bedroom', 'pool', 'wine-cellar'],
    images: [
      { url: '/api/placeholder/800/600', alt: 'Luxury Villa - Exterior', isPrimary: true },
      { url: '/api/placeholder/800/600', alt: 'Luxury Villa - Pool Area', isPrimary: false },
      { url: '/api/placeholder/800/600', alt: 'Luxury Villa - Interior', isPrimary: false },
    ],
    files: [
      { name: 'Complete Plans.pdf', url: '/files/luxury-villa-complete.pdf', type: 'pdf', size: 5120000 },
      { name: 'Architectural Drawings.dwg', url: '/files/luxury-villa-arch.dwg', type: 'dwg', size: 2048000 },
      { name: 'Landscape Plans.pdf', url: '/files/luxury-villa-landscape.pdf', type: 'pdf', size: 1536000 },
    ],
    specifications: {
      dimensions: { width: 25, height: 18, depth: 30, unit: 'm' },
      area: { value: 450, unit: 'sqm' },
      bedrooms: 5,
      bathrooms: 4,
      floors: 2,
      style: 'Luxury',
      features: ['Swimming Pool', 'Wine Cellar', 'Home Theater', 'Master Suite', 'Guest House'],
    },
    status: 'published',
    featured: true,
    bestseller: false,
    downloadCount: 89,
    rating: { average: 4.9, count: 15 },
    seo: {
      metaTitle: 'Luxury 5-Bedroom Villa House Plan with Pool - Premium Design',
      metaDescription: 'Elegant luxury villa house plan with swimming pool, wine cellar, and premium amenities.',
      slug: 'luxury-villa-with-pool',
    },
  },
  {
    title: 'Dashboard UI Kit - Modern Admin',
    description: 'A comprehensive dashboard UI kit with 50+ components, dark/light themes, and responsive design. Perfect for admin panels and data visualization.',
    shortDescription: 'Complete dashboard UI components with dark/light themes',
    price: 89,
    category: 'ui-kits',
    subcategory: 'dashboard',
    tags: ['ui-kit', 'dashboard', 'admin', 'components', 'responsive'],
    images: [
      { url: '/api/placeholder/800/600', alt: 'Dashboard UI Kit - Overview', isPrimary: true },
      { url: '/api/placeholder/800/600', alt: 'Dashboard UI Kit - Components', isPrimary: false },
      { url: '/api/placeholder/800/600', alt: 'Dashboard UI Kit - Dark Theme', isPrimary: false },
    ],
    files: [
      { name: 'Figma File.fig', url: '/files/dashboard-ui-kit.fig', type: 'fig', size: 15360000 },
      { name: 'Sketch File.sketch', url: '/files/dashboard-ui-kit.sketch', type: 'sketch', size: 12288000 },
      { name: 'Adobe XD.xd', url: '/files/dashboard-ui-kit.xd', type: 'xd', size: 10240000 },
    ],
    specifications: {
      features: ['50+ Components', 'Dark/Light Themes', 'Responsive Design', 'Icon Library', 'Style Guide'],
    },
    status: 'published',
    featured: true,
    bestseller: false,
    downloadCount: 234,
    rating: { average: 4.7, count: 41 },
    seo: {
      metaTitle: 'Modern Dashboard UI Kit - Admin Panel Components',
      metaDescription: 'Professional dashboard UI kit with 50+ components, themes, and responsive design.',
      slug: 'dashboard-ui-kit-modern-admin',
    },
  },
];

async function seedDatabase() {
  const client = new MongoClient(uri);
  
  try {
    await client.connect();
    console.log('Connected to MongoDB');
    
    const db = client.db();
    
    // Clear existing products
    await db.collection('products').deleteMany({});
    console.log('Cleared existing products');
    
    // Insert sample products
    const result = await db.collection('products').insertMany(sampleProducts);
    console.log(`Inserted ${result.insertedCount} products`);
    
    // Create indexes
    await db.collection('products').createIndex({ title: 'text', description: 'text', tags: 'text' });
    await db.collection('products').createIndex({ category: 1, status: 1 });
    await db.collection('products').createIndex({ featured: 1, status: 1 });
    await db.collection('products').createIndex({ 'seo.slug': 1 }, { unique: true });
    console.log('Created indexes');
    
    console.log('Database seeded successfully!');
  } catch (error) {
    console.error('Error seeding database:', error);
  } finally {
    await client.close();
  }
}

if (require.main === module) {
  seedDatabase();
}

module.exports = { seedDatabase, sampleProducts };
