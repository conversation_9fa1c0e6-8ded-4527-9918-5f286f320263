'use client';

import { useState, useEffect } from 'react';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import { useCart } from '@/components/providers/CartProvider';
import { toast } from '@/components/ui/Toaster';
import { ShoppingCartIcon, EyeIcon, MagnifyingGlassIcon } from '@heroicons/react/24/outline';

export default function ShopPage() {
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [filters, setFilters] = useState({
    category: '',
    search: '',
    priceRange: '',
  });
  const { addItem } = useCart();

  useEffect(() => {
    fetchProducts();
  }, [filters]);

  const fetchProducts = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams();
      
      if (filters.category) params.append('category', filters.category);
      if (filters.search) params.append('search', filters.search);
      
      const response = await fetch(`/api/products?${params}`);
      
      if (response.ok) {
        const data = await response.json();
        setProducts(data.products);
      } else {
        // Fallback to mock data if API fails
        setProducts(getMockProducts());
      }
    } catch (error) {
      console.error('Error fetching products:', error);
      setProducts(getMockProducts());
    } finally {
      setLoading(false);
    }
  };

  const getMockProducts = () => [
    {
      _id: '1',
      title: 'Modern 4-Bedroom House Plan',
      shortDescription: 'Contemporary design with open floor plan',
      price: 299,
      salePrice: 199,
      category: 'house-plans',
      images: [{ url: '/api/placeholder/400/300', alt: 'Modern House Plan', isPrimary: true }],
      specifications: { bedrooms: 4, bathrooms: 3, area: { value: 250, unit: 'sqm' } },
      featured: true,
      bestseller: true,
    },
    {
      _id: '2',
      title: 'UI Kit - Dashboard Design',
      shortDescription: 'Complete dashboard UI components',
      price: 89,
      category: 'ui-kits',
      images: [{ url: '/api/placeholder/400/300', alt: 'UI Kit', isPrimary: true }],
      featured: true,
    },
    // Add more mock products...
  ];

  const handleAddToCart = (product) => {
    addItem({
      id: product._id,
      title: product.title,
      price: product.price,
      salePrice: product.salePrice,
      image: product.images[0]?.url,
    });
    toast.success('Added to cart!');
  };

  const categories = [
    { value: '', label: 'All Categories' },
    { value: 'house-plans', label: 'House Plans' },
    { value: 'ui-kits', label: 'UI Kits' },
    { value: 'templates', label: 'Templates' },
    { value: 'graphics', label: 'Graphics' },
    { value: 'mockups', label: 'Mockups' },
  ];

  return (
    <div className="min-h-screen bg-white">
      <Header />
      
      <main className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-8">
        {/* Page Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">Shop All Products</h1>
          <p className="text-lg text-gray-600">
            Discover our complete collection of premium design templates and digital assets
          </p>
        </div>

        {/* Filters */}
        <div className="mb-8 flex flex-col sm:flex-row gap-4">
          {/* Search */}
          <div className="relative flex-1">
            <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
            <input
              type="text"
              placeholder="Search products..."
              value={filters.search}
              onChange={(e) => setFilters({ ...filters, search: e.target.value })}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          {/* Category Filter */}
          <select
            value={filters.category}
            onChange={(e) => setFilters({ ...filters, category: e.target.value })}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            {categories.map((category) => (
              <option key={category.value} value={category.value}>
                {category.label}
              </option>
            ))}
          </select>
        </div>

        {/* Products Grid */}
        {loading ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            {[...Array(8)].map((_, i) => (
              <div key={i} className="animate-pulse">
                <div className="bg-gray-200 h-48 rounded-lg mb-4"></div>
                <div className="h-4 bg-gray-200 rounded mb-2"></div>
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
              </div>
            ))}
          </div>
        ) : (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            {products.map((product) => (
              <div key={product._id} className="group relative bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-lg transition-shadow">
                {/* Product Image */}
                <div className="relative overflow-hidden rounded-t-lg">
                  <img
                    src={product.images[0]?.url}
                    alt={product.images[0]?.alt}
                    className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                  {product.bestseller && (
                    <span className="absolute top-2 left-2 bg-red-500 text-white text-xs px-2 py-1 rounded">
                      Bestseller
                    </span>
                  )}
                  {product.salePrice && (
                    <span className="absolute top-2 right-2 bg-green-500 text-white text-xs px-2 py-1 rounded">
                      Sale
                    </span>
                  )}
                  
                  {/* Hover Actions */}
                  <div className="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center space-x-2">
                    <button className="bg-white text-gray-900 p-2 rounded-full hover:bg-gray-100 transition-colors">
                      <EyeIcon className="h-5 w-5" />
                    </button>
                    <button
                      onClick={() => handleAddToCart(product)}
                      className="bg-blue-600 text-white p-2 rounded-full hover:bg-blue-700 transition-colors"
                    >
                      <ShoppingCartIcon className="h-5 w-5" />
                    </button>
                  </div>
                </div>

                {/* Product Info */}
                <div className="p-4">
                  <h3 className="font-semibold text-gray-900 mb-2 line-clamp-2">
                    {product.title}
                  </h3>
                  <p className="text-sm text-gray-600 mb-3 line-clamp-2">
                    {product.shortDescription}
                  </p>
                  
                  {/* Specifications for house plans */}
                  {product.category === 'house-plans' && product.specifications && (
                    <div className="flex text-xs text-gray-500 space-x-3 mb-3">
                      {product.specifications.bedrooms && (
                        <span>{product.specifications.bedrooms} bed</span>
                      )}
                      {product.specifications.bathrooms && (
                        <span>{product.specifications.bathrooms} bath</span>
                      )}
                      {product.specifications.area && (
                        <span>{product.specifications.area.value} {product.specifications.area.unit}</span>
                      )}
                    </div>
                  )}

                  {/* Price */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      {product.salePrice ? (
                        <>
                          <span className="text-lg font-bold text-gray-900">
                            ${product.salePrice}
                          </span>
                          <span className="text-sm text-gray-500 line-through">
                            ${product.price}
                          </span>
                        </>
                      ) : (
                        <span className="text-lg font-bold text-gray-900">
                          ${product.price}
                        </span>
                      )}
                    </div>
                    <button
                      onClick={() => handleAddToCart(product)}
                      className="text-blue-600 hover:text-blue-700 transition-colors"
                    >
                      <ShoppingCartIcon className="h-5 w-5" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {products.length === 0 && !loading && (
          <div className="text-center py-12">
            <p className="text-gray-500 text-lg">No products found matching your criteria.</p>
          </div>
        )}
      </main>

      <Footer />
    </div>
  );
}
