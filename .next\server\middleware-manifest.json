{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_c54a1250._.js", "server/edge/chunks/[root-of-the-server]__e07e566a._.js", "server/edge/chunks/edge-wrapper_60e6f275.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/admin(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(\\\\.json)?[\\/#\\?]?$", "originalSource": "/admin/:path*"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/account(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(\\\\.json)?[\\/#\\?]?$", "originalSource": "/account/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "0KKkVeSHVNC/o+yUjjzzeS/zBOKtzlGv/jpVHdLq4KE=", "__NEXT_PREVIEW_MODE_ID": "ec77100f680d0be6e989ab81ea5b9ced", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "49ce9b3dd28621d0f1476a8dde68d577fed800a0ac21edb8bec4aa578672574d", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "b7465b35b61c03e3e541cb07d5736e93cbe220af3efdbd75fefcac1a11a22c80"}}}, "sortedMiddleware": ["/"], "functions": {}}