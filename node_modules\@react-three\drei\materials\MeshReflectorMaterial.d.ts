import { Matrix4, MeshStandardMaterial, Texture } from 'three';
export declare class MeshReflectorMaterial extends MeshStandardMaterial {
    private _tDepth;
    private _distortionMap;
    private _tDiffuse;
    private _tDiffuseBlur;
    private _textureMatrix;
    private _hasBlur;
    private _mirror;
    private _mixBlur;
    private _blurStrength;
    private _minDepthThreshold;
    private _maxDepthThreshold;
    private _depthScale;
    private _depthToBlurRatioBias;
    private _distortion;
    private _mixContrast;
    constructor(parameters?: {});
    onBeforeCompile(shader: any): void;
    get tDiffuse(): Texture | null;
    set tDiffuse(v: Texture | null);
    get tDepth(): Texture | null;
    set tDepth(v: Texture | null);
    get distortionMap(): Texture | null;
    set distortionMap(v: Texture | null);
    get tDiffuseBlur(): Texture | null;
    set tDiffuseBlur(v: Texture | null);
    get textureMatrix(): Matrix4 | null;
    set textureMatrix(v: Matrix4 | null);
    get hasBlur(): boolean;
    set hasBlur(v: boolean);
    get mirror(): number;
    set mirror(v: number);
    get mixBlur(): number;
    set mixBlur(v: number);
    get mixStrength(): number;
    set mixStrength(v: number);
    get minDepthThreshold(): number;
    set minDepthThreshold(v: number);
    get maxDepthThreshold(): number;
    set maxDepthThreshold(v: number);
    get depthScale(): number;
    set depthScale(v: number);
    get depthToBlurRatioBias(): number;
    set depthToBlurRatioBias(v: number);
    get distortion(): number;
    set distortion(v: number);
    get mixContrast(): number;
    set mixContrast(v: number);
}
