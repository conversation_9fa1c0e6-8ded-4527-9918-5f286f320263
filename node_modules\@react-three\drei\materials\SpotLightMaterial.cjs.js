"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("three"),n=require("../helpers/constants.cjs.js");function t(e){if(e&&e.__esModule)return e;var n=Object.create(null);return e&&Object.keys(e).forEach((function(t){if("default"!==t){var o=Object.getOwnPropertyDescriptor(e,t);Object.defineProperty(n,t,o.get?o:{enumerable:!0,get:function(){return e[t]}})}})),n.default=e,Object.freeze(n)}var o=t(e);class r extends o.ShaderMaterial{constructor(){super({uniforms:{depth:{value:null},opacity:{value:1},attenuation:{value:2.5},anglePower:{value:12},spotPosition:{value:new o.Vector3(0,0,0)},lightColor:{value:new o.Color("white")},cameraNear:{value:0},cameraFar:{value:1},resolution:{value:new o.Vector2(0,0)}},transparent:!0,depthWrite:!1,vertexShader:"\n        varying vec3 vNormal;\n        varying float vViewZ;\n        varying float vIntensity;\n        uniform vec3 spotPosition;\n        uniform float attenuation;\n\n        #include <common>\n        #include <logdepthbuf_pars_vertex>\n\n        void main() {\n          // compute intensity\n          vNormal = normalize(normalMatrix * normal);\n          vec4 worldPosition = modelMatrix * vec4(position, 1);\n          vec4 viewPosition = viewMatrix * worldPosition;\n          vViewZ = viewPosition.z;\n\n          vIntensity = 1.0 - saturate(distance(worldPosition.xyz, spotPosition) / attenuation);\n\n          gl_Position = projectionMatrix * viewPosition;\n\n          #include <logdepthbuf_vertex>\n        }\n      ",fragmentShader:`\n        varying vec3 vNormal;\n        varying float vViewZ;\n        varying float vIntensity;\n\n        uniform vec3 lightColor;\n        uniform float anglePower;\n        uniform sampler2D depth;\n        uniform vec2 resolution;\n        uniform float cameraNear;\n        uniform float cameraFar;\n        uniform float opacity;\n\n        #include <packing>\n        #include <logdepthbuf_pars_fragment>\n\n        float readDepth(sampler2D depthSampler, vec2 uv) {\n          float fragCoordZ = texture(depthSampler, uv).r;\n\n          // https://github.com/mrdoob/three.js/issues/23072\n          #ifdef USE_LOGDEPTHBUF\n            float viewZ = 1.0 - exp2(fragCoordZ * log(cameraFar + 1.0) / log(2.0));\n          #else\n            float viewZ = perspectiveDepthToViewZ(fragCoordZ, cameraNear, cameraFar);\n          #endif\n\n          return viewZ;\n        }\n\n        void main() {\n          #include <logdepthbuf_fragment>\n\n          vec3 normal = vec3(vNormal.x, vNormal.y, abs(vNormal.z));\n          float angleIntensity = pow(dot(normal, vec3(0, 0, 1)), anglePower);\n          float intensity = vIntensity * angleIntensity;\n\n          // fades when z is close to sampled depth, meaning the cone is intersecting existing geometry\n          bool isSoft = resolution[0] > 0.0 && resolution[1] > 0.0;\n          if (isSoft) {\n            vec2 uv = gl_FragCoord.xy / resolution;\n            intensity *= smoothstep(0.0, 1.0, vViewZ - readDepth(depth, uv));\n          }\n\n          gl_FragColor = vec4(lightColor, intensity * opacity);\n\n          #include <tonemapping_fragment>\n          #include <${n.version>=154?"colorspace_fragment":"encodings_fragment"}>\n        }\n      `})}}exports.SpotLightMaterial=r;
