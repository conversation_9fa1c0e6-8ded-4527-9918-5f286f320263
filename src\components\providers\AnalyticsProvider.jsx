'use client';

import { useEffect } from 'react';
import { usePathname, useSearchParams } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { initGA, trackPageView, setUserContext } from '@/lib/analytics';
import { initSentry, setUserContext as setSentryUser } from '@/lib/sentry';

export function AnalyticsProvider({ children }) {
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const { data: session } = useSession();

  // Initialize analytics and monitoring
  useEffect(() => {
    initGA();
    initSentry();
  }, []);

  // Track page views
  useEffect(() => {
    const url = pathname + (searchParams.toString() ? `?${searchParams.toString()}` : '');
    trackPageView(url);
  }, [pathname, searchParams]);

  // Set user context for analytics and error tracking
  useEffect(() => {
    if (session?.user) {
      const user = {
        id: session.user.id,
        email: session.user.email,
        name: session.user.name,
        role: session.user.role,
      };
      
      setUserContext(user);
      setSentryUser(user);
    }
  }, [session]);

  return <>{children}</>;
}
