"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("three-stdlib"),r=require("suspend-react");let t=null;async function n(r){const n=await async function(e){return"string"==typeof e?await(await fetch(e)).json():e}(r);return a=n,t||(t=new e.Font<PERSON>oader),t.parse(a);var a}function a(e){return r.suspend(n,[e])}a.preload=e=>r.preload(n,[e]),a.clear=e=>r.clear([e]),exports.useFont=a;
